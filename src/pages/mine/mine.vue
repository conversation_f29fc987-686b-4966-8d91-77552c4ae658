<template>
    <view class="mine-page" :class="{ 'is-login': isLoggedIn }">
      <!-- 背景渐变 -->
      <view class="page-background"></view>
  
      <uni-nav-bar
        :border="false"
        :fixed="true"
        :background-color="'transparent'"
        status-bar="true"
        :placeholder="true"
      />
  
      <view class="main-content">
        <!-- 用户信息卡片 -->
        <view class="user-card" @click="handleUserCardClick">
          <view class="user-info">
            <image :src="userAvatar" class="avatar" mode="aspectFill" />
            <view class="details">
              <view class="name-section">
                <text class="name">{{ displayName }}</text>
                <view v-if="isLoggedIn && user?.isVip" class="vip-tag">VIP</view>
              </view>
              <view class="intro">{{ userIntro }}</view>
            </view>
          </view>
          <view class="arrow">
            <tui-icon name="arrowright" :size="20" color="#BDBDBD"></tui-icon>
          </view>
        </view>
  
        <!-- 用户数据统计 -->
        <view class="stats-card">
          <view class="stat-item" @click="secureNavigate('/pages/post/mine')">
            <text class="value">{{ isLoggedIn ? user?.posts || 0 : '-' }}</text>
            <text class="label">我的发布</text>
          </view>
          <view class="stat-item" @click="secureNavigate('/pages/mine/collections')">
            <text class="value">{{ isLoggedIn ? user?.followers || 0 : '-' }}</text>
            <text class="label">收藏</text>
          </view>
          <view class="stat-item" @click="secureNavigate('/pages/mine/history')">
            <text class="value">{{ isLoggedIn ? user?.following || 0 : '-' }}</text>
            <text class="label">足迹</text>
          </view>
        </view>
  
        <!-- 我的钱包 -->
        <view class="wallet-card" @click="secureNavigate('/pages/mine/wallet')">
          <view class="card-left">
            <view class="title-section">
              <tui-icon name="wallet" :size="20" color="#ca8a04"></tui-icon>
              <text class="title">我的钱包</text>
            </view>
            <view class="balance-value">
              <template v-if="isLoggedIn">
                <text class="currency">¥</text>
                <text class="amount">{{ user?.balance?.toFixed(2) || '0.00' }}</text>
              </template>
              <template v-else>
                <text class="login-prompt">登录后查看</text>
              </template>
            </view>
          </view>
          <view class="card-right">
            <view class="wallet-btn">
              <text>账单</text>
              <tui-icon name="arrowright" :size="16" color="#ca8a04"></tui-icon>
            </view>
          </view>
        </view>
  
        <!-- 核心功能区 -->
        <view class="section-card">
          <view class="section-title">核心服务</view>
          <view class="grid-menu">
            <view
              v-for="item in coreFeatures"
              :key="item.title"
              class="grid-item"
              @click="handleCoreFeatureClick(item)"
            >
              <view class="grid-icon-wrapper" :style="{ backgroundColor: item.bgColor }">
                <tui-icon :name="item.iconName" :size="26" :color="item.iconColor"></tui-icon>
              </view>
              <text class="grid-label">{{ item.title }}</text>
            </view>
          </view>
        </view>
  
        <!-- 通用功能区 -->
        <view class="section-card">
          <view class="section-title">通用设置</view>
          <view class="list-menu">
            <view
              v-for="item in otherFeatures"
              :key="item.title"
              class="list-item"
              @click="handleMenuItemClick(item)"
            >
              <view class="list-item-left">
                <view class="list-icon-wrapper" :style="{ backgroundColor: item.bgColor }">
                  <tui-icon :name="item.iconName" :size="20" :color="item.iconColor"></tui-icon>
                </view>
                <text class="list-label">{{ item.title }}</text>
              </view>
              <view class="list-item-right">
                <tui-icon name="arrowright" :size="18" color="#d1d1d1"></tui-icon>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </template>
  
  <script setup lang="ts">
  import { computed } from 'vue'
  import { useUserStore } from '@/stores/user'
  
  const userStore = useUserStore()
  
  const isLoggedIn = computed(() => userStore.isLoggedIn)
  const user = computed(() => userStore.user)
  
  const displayName = computed(() => {
    return isLoggedIn.value ? userStore.displayName : '您好，请登录'
  })
  
  const userAvatar = computed(() => {
    return isLoggedIn.value ? userStore.userAvatar : '/static/images/default-avatar.png'
  })
  
  const userIntro = computed(() => {
    return isLoggedIn.value ? user.value?.intro || '这个人很神秘，什么也没留下~' : '登录后解锁更多精彩内容'
  })
  
  // 核心功能菜单
  const coreFeatures = [
    {
      title: '我的发布',
      iconName: 'paperplane',
      bgColor: '#EBF5FF',
      iconColor: '#3B82F6',
      path: '/pages/post/mine',
    },
    {
      title: '我的收藏',
      iconName: 'star',
      bgColor: '#FFFBEB',
      iconColor: '#F59E0B',
      path: '/pages/mine/collections',
    },
    {
      title: '浏览历史',
      iconName: 'time',
      bgColor: '#ECFDF5',
      iconColor: '#10B981',
      path: '/pages/mine/history',
    },
    {
      title: '在线客服',
      iconName: 'service',
      bgColor: '#EFF6FF',
      iconColor: '#6366F1',
      action: 'contact',
    },
  ]
  
  // 其他功能菜单
  const otherFeatures = [
    {
      title: '账号设置',
      iconName: 'setup',
      bgColor: '#EFF6FF',
      iconColor: '#6366F1',
      path: '/pages/mine/settings',
      action: 'navigate',
    },
    {
      title: '隐私设置',
      iconName: 'lock',
      bgColor: '#F0F9FF',
      iconColor: '#0EA5E9',
      path: '/pages/mine/privacy',
      action: 'navigate',
    },
    {
      title: '关于我们',
      iconName: 'info',
      bgColor: '#F3F4F6',
      iconColor: '#6B7280',
      path: '/pages/mine/about',
      action: 'navigate',
    },
    {
      title: '退出登录',
      iconName: 'logout',
      bgColor: '#FEF2F2',
      iconColor: '#EF4444',
      path: '',
      action: 'logout',
    },
  ]
  
  // 模拟登录
  const handleLogin = () => {
    userStore.mockLogin()
  }
  
  // 带登录验证的导航
  const secureNavigate = (path: string) => {
    if (!isLoggedIn.value) {
      uni.showModal({
        title: '请先登录',
        content: '登录后即可使用此功能',
        success: (res) => {
          if (res.confirm) {
            handleLogin()
          }
        },
      })
      return
    }
    navigateTo(path)
  }
  
  // 用户卡片点击
  const handleUserCardClick = () => {
    if (isLoggedIn.value) {
      navigateTo('/pages/mine/profile')
    } else {
      handleLogin()
    }
  }
  
  // 核心功能点击处理
  const handleCoreFeatureClick = (item: (typeof coreFeatures)[0]) => {
    if (item.action === 'contact') {
      uni.showToast({
        title: '客服功能开发中',
        icon: 'none'
      })
    } else if (item.path) {
      secureNavigate(item.path)
    }
  }

  // 菜单项点击处理
  const handleMenuItemClick = (item: (typeof otherFeatures)[0]) => {
    if (item.action === 'navigate') {
      secureNavigate(item.path)
    } else if (item.action === 'contact') {
      uni.makePhoneCall({
        phoneNumber: '18888888888', // 在这里替换为实际的客服电话
      })
    } else if (item.action === 'logout') {
      if (!isLoggedIn.value) return
  
      uni.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            userStore.clearUserInfo()
          }
        },
      })
    }
  }
  
  // 统一的页面跳转
  const navigateTo = (path: string) => {
    if (!path) return
    uni.navigateTo({ url: path })
  }
  </script>
  
  <style lang="scss" scoped>
  .mine-page {
    position: relative;
    min-height: 100vh;
    padding-bottom: 48rpx;
    background-color: var(--bg-page);
    overflow-x: hidden;
  
    &.is-login {
      .page-background {
        height: 420rpx;
      }
    }
  }
  
  .page-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 250rpx;
    background: linear-gradient(175deg, #dbeafe 5%, #eef2ff 40%, var(--bg-page) 80%);
    transition: height 0.4s ease-out;
    z-index: 0;
  }
  
  .main-content {
    position: relative;
    z-index: 1;
    padding: 0 32rpx;
  }
  
  .user-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 0;
    margin-bottom: 16rpx;
  
    .user-info {
      display: flex;
      align-items: center;
      gap: 24rpx;
  
      .avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        border: 4rpx solid #ffffff;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
      }
  
      .details {
        .name-section {
          display: flex;
          align-items: center;
          gap: 16rpx;
        }
        .name {
          font-size: 40rpx;
          font-weight: 600;
          color: var(--text-base);
        }
        .vip-tag {
          background: linear-gradient(135deg, #fde047, #facc15);
          color: #854d0e;
          padding: 4rpx 12rpx;
          border-radius: 8rpx;
          font-size: 20rpx;
          font-weight: bold;
        }
        .intro {
          font-size: 24rpx;
          color: var(--text-secondary);
          margin-top: 8rpx;
          max-width: 400rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  
  .stats-card {
    display: flex;
    justify-content: space-around;
    background-color: var(--bg-card);
    padding: 32rpx;
    border-radius: var(--radius-lg);
    box-shadow: 0 8rpx 40rpx rgba(var(--primary-rgb), 0.06);
    margin-bottom: 32rpx;
  
    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8rpx;
      .value {
        font-size: 36rpx;
        font-weight: 700;
        color: var(--text-base);
      }
      .label {
        font-size: 24rpx;
        color: var(--text-secondary);
      }
    }
  }
  
  .wallet-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 24rpx;
    padding: 32rpx;
    position: relative;
    overflow: hidden;
    margin: 0 32rpx 32rpx;
  
    &::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -20%;
      width: 200rpx;
      height: 200rpx;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
    }
  
    .card-left {
      .title-section {
        display: flex;
        align-items: center;
        gap: 12rpx;
        margin-bottom: 12rpx;
        .title {
          font-size: 28rpx;
          font-weight: 500;
          color: #fff;
        }
      }
      .balance-value {
        font-weight: 700;
        color: #fff;
        .currency {
          font-size: 28rpx;
          margin-right: 4rpx;
        }
        .amount {
          font-size: 40rpx;
        }
        .login-prompt {
          font-size: 28rpx;
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  
    .card-right {
      .wallet-btn {
        display: flex;
        align-items: center;
        gap: 4rpx;
        background-color: #fef3c7;
        color: #ca8a04;
        padding: 12rpx 20rpx;
        border-radius: 30rpx;
        font-size: 24rpx;
      }
    }
  }
  
  .section-card {
    background-color: var(--bg-card);
    padding: 32rpx;
    border-radius: var(--radius-lg);
    box-shadow: 0 8rpx 40rpx rgba(var(--primary-rgb), 0.06);
    margin-bottom: 32rpx;
  
    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      margin-bottom: 32rpx;
      color: var(--text-base);
    }
  }
  
  .grid-menu {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 32rpx;
  
    .grid-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16rpx;
  
      .grid-icon-wrapper {
        width: 100rpx;
        height: 100rpx;
        border-radius: var(--radius-lg);
        display: flex;
        justify-content: center;
        align-items: center;
        transition: transform 0.2s ease;
      }
  
      &:active .grid-icon-wrapper {
        transform: scale(0.92);
      }
  
      .grid-label {
        font-size: 24rpx;
        color: var(--text-secondary);
      }
    }
  }
  
  .list-menu {
    display: flex;
    flex-direction: column;
  
    .list-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      transition: background-color 0.15s ease;
      margin: 0 -32rpx;
      padding: 24rpx 32rpx;
  
      &:active {
        background-color: #f9fafb;
      }
    }
    .list-item-left {
      display: flex;
      align-items: center;
      gap: 24rpx;
    }
  
    .list-icon-wrapper {
      width: 64rpx;
      height: 64rpx;
      border-radius: 100rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  
    .list-label {
      font-size: 28rpx;
      color: var(--text-base);
    }
  }
  </style>
  