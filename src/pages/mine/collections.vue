<template>
  <view class="collections-page">
    <CustomNavBar title="我的收藏" />
    <EmptyResult
      message="您的收藏夹是空的"
      sub-message="快去逛逛，发现喜欢的内容吧"
      icon="i-carbon-star"
    />
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar.vue'
import EmptyResult from '@/components/common/EmptyResult.vue'
</script>

<style lang="scss" scoped>
.collections-page {
  background-color: var(--bg-page);
  min-height: 100vh;
}
</style> 