<template>
  <view class="login-container">
    <!-- 背景装饰 -->
    <view class="login-bg">
      <view class="bg-gradient"></view>
      <view class="bg-circles">
        <view class="circle circle-1"></view>
        <view class="circle circle-2"></view>
        <view class="circle circle-3"></view>
      </view>
    </view>

    <!-- 顶部导航 -->
    <uni-nav-bar
      :border="false"
      fixed
      background-color="transparent"
      :status-bar="true"
    >
      <template #left>
        <view class="nav-left" @tap="goBack">
          <text class="i-carbon-arrow-left text-40rpx text-white"></text>
        </view>
      </template>
    </uni-nav-bar>

    <!-- 登录内容 -->
    <view class="login-content">
      <!-- Logo和标题 -->
      <view class="login-header">
        <view class="logo-wrapper">
          <image src="/static/logo.png" class="logo" mode="aspectFit" />
        </view>
        <text class="app-title">BDB招聘平台</text>
        <text class="app-subtitle">连接优秀人才与理想职位</text>
      </view>

      <!-- 登录卡片 -->
      <view class="login-card">
        <view class="card-header">
          <text class="card-title">欢迎登录</text>
          <text class="card-subtitle">请选择登录方式</text>
        </view>

        <!-- 登录方式 -->
        <view class="login-methods">
          <!-- WeChat登录 -->
          <view class="login-method wechat-login" @tap="handleWeChatLogin">
            <view class="method-icon">
              <text class="i-carbon-logo-wechat text-60rpx text-green"></text>
            </view>
            <view class="method-content">
              <text class="method-title">微信快速登录</text>
              <text class="method-desc">使用微信账号一键登录</text>
            </view>
            <text class="i-carbon-chevron-right text-32rpx text-grey"></text>
          </view>

          <!-- 手机号登录 -->
          <view class="login-method phone-login" @tap="handlePhoneLogin">
            <view class="method-icon">
              <text class="i-carbon-phone text-60rpx text-blue"></text>
            </view>
            <view class="method-content">
              <text class="method-title">手机号登录</text>
              <text class="method-desc">使用手机号验证码登录</text>
            </view>
            <text class="i-carbon-chevron-right text-32rpx text-grey"></text>
          </view>

          <!-- Mock登录 (仅测试用) -->
          <view class="login-method mock-login" @tap="handleMockLogin" v-if="isDev">
            <view class="method-icon">
              <text class="i-carbon-user-avatar text-60rpx text-purple"></text>
            </view>
            <view class="method-content">
              <text class="method-title">测试登录</text>
              <text class="method-desc">开发测试专用登录</text>
            </view>
            <text class="i-carbon-chevron-right text-32rpx text-grey"></text>
          </view>
        </view>

        <!-- 协议条款 -->
        <view class="agreement-section">
          <view class="agreement-checkbox" @tap="toggleAgreement">
            <text 
              :class="[
                'i-carbon-checkmark-outline text-32rpx',
                isAgreed ? 'text-primary' : 'text-grey'
              ]"
            ></text>
          </view>
          <text class="agreement-text">
            我已阅读并同意
            <text class="agreement-link" @tap="showPrivacyPolicy">《隐私政策》</text>
            和
            <text class="agreement-link" @tap="showUserAgreement">《用户协议》</text>
          </text>
        </view>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="login-footer">
      <text class="footer-text">安全登录 · 保护隐私</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useUserStore } from "@/stores/user";

const userStore = useUserStore();

// 状态管理
const isAgreed = ref(false);
const isDev = computed(() => import.meta.env.DEV);

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 切换协议同意状态
const toggleAgreement = () => {
  isAgreed.value = !isAgreed.value;
};

// 检查协议同意状态
const checkAgreement = () => {
  if (!isAgreed.value) {
    uni.showToast({
      title: "请先同意用户协议和隐私政策",
      icon: "none",
      duration: 2000
    });
    return false;
  }
  return true;
};

// WeChat登录
const handleWeChatLogin = async () => {
  if (!checkAgreement()) return;

  const { login: weChatLogin, isWeChatEnv } = useWeChat();

  if (!isWeChatEnv()) {
    uni.showModal({
      title: "提示",
      content: "微信登录仅在微信小程序中可用，当前将使用模拟登录",
      showCancel: false,
      success: () => {
        handleMockLogin();
      }
    });
    return;
  }

  uni.showLoading({ title: "正在启动微信登录..." });

  try {
    const result = await weChatLogin();
    uni.hideLoading();

    if (result.success) {
      uni.showToast({
        title: "登录成功",
        icon: "success",
        duration: 1500,
        success: () => {
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      });
    } else {
      uni.showToast({
        title: result.error || "登录失败",
        icon: "none",
        duration: 2000
      });
    }
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: "登录失败，请重试",
      icon: "none",
      duration: 2000
    });
  }
};

// 手机号登录
const handlePhoneLogin = () => {
  if (!checkAgreement()) return;
  
  uni.navigateTo({
    url: "/pages/auth/phone-login"
  });
};

// Mock登录 (测试用)
const handleMockLogin = () => {
  if (!checkAgreement()) return;
  
  const mockUser = {
    id: "mock_user_001",
    name: "张三",
    avatar: "/static/images/mock-avatar.png",
    intro: "资深前端开发工程师，专注于移动端开发",
    isVip: true,
    followers: 128,
    following: 89,
    posts: 45,
    phone: "138****8888",
    wechat: "zhangsan_dev",
    email: "<EMAIL>",
    joinDate: "2023-01-15"
  };
  
  uni.showLoading({ title: "登录中..." });
  
  setTimeout(() => {
    userStore.setUserInfo(mockUser, "mock_access_token_123456");
    uni.hideLoading();
    
    uni.showToast({
      title: "登录成功",
      icon: "success",
      duration: 1500,
      success: () => {
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }
    });
  }, 1000);
};

// 显示隐私政策
const showPrivacyPolicy = () => {
  uni.navigateTo({
    url: "/pages/about/privacy"
  });
};

// 显示用户协议
const showUserAgreement = () => {
  uni.navigateTo({
    url: "/pages/about/agreement"
  });
};
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.login-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60vh;
  overflow: hidden;
}

.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    var(--primary-400) 0%,
    var(--primary-600) 50%,
    var(--primary-800) 100%
  );
}

.bg-circles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
  width: 300rpx;
  height: 300rpx;
  top: -100rpx;
  right: -100rpx;
}

.circle-2 {
  width: 200rpx;
  height: 200rpx;
  top: 200rpx;
  left: -50rpx;
}

.circle-3 {
  width: 150rpx;
  height: 150rpx;
  bottom: 100rpx;
  right: 50rpx;
}

/* 导航 */
.nav-left {
  padding-left: var(--spacing-20);
}

/* 登录内容 */
.login-content {
  position: relative;
  z-index: 2;
  padding: 200rpx var(--spacing-20) var(--spacing-40);
}

/* 登录头部 */
.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: var(--spacing-40);
}

.logo-wrapper {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: var(--spacing-20);
}

.logo {
  width: 100%;
  height: 100%;
}

.app-title {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-bold);
  color: var(--text-inverse);
  margin-bottom: var(--spacing-8);
}

.app-subtitle {
  font-size: var(--font-size-base);
  color: rgba(255, 255, 255, 0.8);
}

/* 登录卡片 */
.login-card {
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  padding: var(--spacing-32);
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  text-align: center;
  margin-bottom: var(--spacing-32);
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
  display: block;
  margin-bottom: var(--spacing-8);
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-info);
}

/* 登录方式 */
.login-methods {
  margin-bottom: var(--spacing-32);
}

.login-method {
  display: flex;
  align-items: center;
  padding: var(--spacing-24);
  border-radius: var(--radius-lg);
  border: 1rpx solid var(--border-color);
  margin-bottom: var(--spacing-16);
  transition: all 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &:active {
    background: var(--bg-tag);
    transform: scale(0.98);
  }
}

.method-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-20);
}

.method-content {
  flex: 1;
}

.method-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-base);
  display: block;
  margin-bottom: var(--spacing-4);
}

.method-desc {
  font-size: var(--font-size-sm);
  color: var(--text-info);
}

/* 协议条款 */
.agreement-section {
  display: flex;
  align-items: flex-start;
  padding: var(--spacing-16) 0;
}

.agreement-checkbox {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-12);
  margin-top: 2rpx;
}

.agreement-text {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--text-info);
  line-height: var(--line-height-normal);
}

.agreement-link {
  color: var(--primary);
  text-decoration: underline;
}

/* 底部 */
.login-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-32);
  text-align: center;
}

.footer-text {
  font-size: var(--font-size-sm);
  color: rgba(255, 255, 255, 0.6);
}
</style>
