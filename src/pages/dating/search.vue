<template>
  <view class="search-page">
    <!-- 自定义导航栏 -->
    <uni-nav-bar
      :border="false"
      fixed
      background-color="#fff"
      :status-bar="true"
      left-icon="back"
      @clickLeft="goBack"
    >
      <template #default>
        <view class="search-container">
          <view class="search-input-wrapper">
            <text class="i-carbon-search search-icon"></text>
            <input
              v-model="searchKeyword"
              class="search-input"
              placeholder="搜索心动的TA"
              confirm-type="search"
              @confirm="performSearch"
              @input="onSearchInput"
              focus
            />
            <text
              v-if="searchKeyword"
              class="i-carbon-close clear-icon"
              @tap="clearSearch"
            ></text>
          </view>
        </view>
      </template>
      <template #right>
        <view class="nav-right" @tap="showFilterModal">
          <text class="filter-text">筛选</text>
          <text class="i-carbon-filter filter-icon"></text>
        </view>
      </template>
    </uni-nav-bar>

    <!-- 搜索内容 -->
    <view class="search-content">
      <!-- 搜索建议 -->
      <view v-if="!searchKeyword && !hasSearched" class="search-suggestions">
        <!-- 热门搜索 -->
        <view class="suggestion-section">
          <view class="section-header">
            <text class="i-carbon-fire section-icon"></text>
            <text class="section-title">热门搜索</text>
          </view>
          <view class="tags-container">
            <text
              v-for="tag in hotSearchTags"
              :key="tag"
              class="tag-item"
              @tap="searchByTag(tag)"
            >
              {{ tag }}
            </text>
          </view>
        </view>

        <!-- 搜索历史 -->
        <view v-if="searchHistory.length > 0" class="suggestion-section">
          <view class="section-header">
            <text class="i-carbon-time section-icon"></text>
            <text class="section-title">搜索历史</text>
            <text class="clear-history" @tap="clearHistory">清空</text>
          </view>
          <view class="history-list">
            <view
              v-for="(item, index) in searchHistory"
              :key="index"
              class="history-item"
              @tap="searchByHistory(item)"
            >
              <text class="history-text">{{ item }}</text>
              <text
                class="i-carbon-close remove-icon"
                @tap.stop="removeHistory(index)"
              ></text>
            </view>
          </view>
        </view>

        <!-- 推荐用户 -->
        <view class="suggestion-section">
          <view class="section-header">
            <text class="i-carbon-user-multiple section-icon"></text>
            <text class="section-title">推荐用户</text>
          </view>
          <view class="recommended-users">
            <view
              v-for="user in recommendedUsers"
              :key="user.id"
              class="user-card"
              @tap="viewUserProfile(user)"
            >
              <image :src="user.avatar" mode="aspectFill" class="user-avatar" />
              <view class="user-info">
                <text class="user-name">{{ user.name }}</text>
                <text class="user-age">{{ user.age }}岁</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 搜索结果 -->
      <view v-if="hasSearched" class="search-results">
        <view class="result-header">
          <text class="result-count">找到{{ searchResults.length }}个结果</text>
          <view class="sort-options">
            <text
              v-for="option in sortOptions"
              :key="option.value"
              :class="['sort-option', { active: currentSort === option.value }]"
              @tap="changeSortOption(option.value)"
            >
              {{ option.label }}
            </text>
          </view>
        </view>

        <!-- 结果列表 -->
        <z-paging
          ref="pagingRef"
          v-model="searchResults"
          @query="querySearchResults"
          :refresher-enabled="false"
          :auto-show-back-to-top="true"
        >
          <view class="results-container">
            <view
              v-for="user in searchResults"
              :key="user.id"
              class="result-card"
              @tap="viewUserProfile(user)"
            >
              <image :src="user.avatar" mode="aspectFill" class="result-avatar" />
              <view class="result-info">
                <view class="result-basic">
                  <text class="result-name">{{ user.name }}</text>
                  <text class="result-age">{{ user.age }}岁</text>
                  <view v-if="user.isOnline" class="online-dot"></view>
                </view>
                <view class="result-location">
                  <text class="i-carbon-location location-icon"></text>
                  <text class="location-text">{{ user.location }} · {{ user.distance }}</text>
                </view>
                <view class="result-tags">
                  <text
                    v-for="tag in user.tags.slice(0, 3)"
                    :key="tag"
                    class="result-tag"
                  >
                    {{ tag }}
                  </text>
                </view>
                <text class="result-intro">{{ user.introduction }}</text>
              </view>
              <view class="result-actions">
                <view class="action-btn like-btn" @tap.stop="likeUser(user.id)">
                  <text class="i-carbon-favorite"></text>
                </view>
              </view>
            </view>
          </view>
        </z-paging>
      </view>

      <!-- 空状态 -->
      <view v-if="hasSearched && searchResults.length === 0" class="empty-state">
        <text class="i-carbon-search-locate empty-icon"></text>
        <text class="empty-title">没有找到相关用户</text>
        <text class="empty-desc">试试其他关键词或调整筛选条件</text>
      </view>
    </view>

    <!-- 筛选弹窗 -->
    <uni-popup ref="filterPopup" type="bottom">
      <view class="filter-modal">
        <view class="filter-header">
          <text class="filter-title">筛选条件</text>
          <text class="reset-btn" @tap="resetFilters">重置</text>
        </view>

        <view class="filter-content">
          <!-- 年龄范围 -->
          <view class="filter-section">
            <text class="filter-label">年龄范围</text>
            <view class="age-range">
              <picker
                mode="selector"
                :range="ageOptions"
                :value="filters.minAgeIndex"
                @change="onMinAgeChange"
              >
                <view class="age-picker">
                  <text>{{ ageOptions[filters.minAgeIndex] }}</text>
                  <text class="i-carbon-chevron-down"></text>
                </view>
              </picker>
              <text class="age-separator">至</text>
              <picker
                mode="selector"
                :range="ageOptions"
                :value="filters.maxAgeIndex"
                @change="onMaxAgeChange"
              >
                <view class="age-picker">
                  <text>{{ ageOptions[filters.maxAgeIndex] }}</text>
                  <text class="i-carbon-chevron-down"></text>
                </view>
              </picker>
            </view>
          </view>

          <!-- 距离范围 -->
          <view class="filter-section">
            <text class="filter-label">距离范围</text>
            <view class="distance-options">
              <text
                v-for="option in distanceOptions"
                :key="option.value"
                :class="['distance-option', { active: filters.distance === option.value }]"
                @tap="selectDistance(option.value)"
              >
                {{ option.label }}
              </text>
            </view>
          </view>

          <!-- 学历要求 -->
          <view class="filter-section">
            <text class="filter-label">学历要求</text>
            <view class="education-options">
              <text
                v-for="option in educationOptions"
                :key="option"
                :class="['education-option', { active: filters.education === option }]"
                @tap="selectEducation(option)"
              >
                {{ option }}
              </text>
            </view>
          </view>

          <!-- 在线状态 -->
          <view class="filter-section">
            <view class="filter-switch">
              <text class="switch-label">只看在线用户</text>
              <switch
                :checked="filters.onlineOnly"
                @change="toggleOnlineOnly"
                color="#667eea"
              />
            </view>
          </view>
        </view>

        <view class="filter-actions">
          <view class="filter-btn cancel-btn" @tap="closeFilterModal">
            <text>取消</text>
          </view>
          <view class="filter-btn confirm-btn" @tap="applyFilters">
            <text>确定</text>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'

// 响应式数据
const searchKeyword = ref('')
const hasSearched = ref(false)
const searchResults = ref([])
const currentSort = ref('distance')
const pagingRef = ref(null)
const filterPopup = ref(null)

// 搜索历史
const searchHistory = ref([
  '温柔善良',
  '阳光开朗',
  '有趣幽默'
])

// 热门搜索标签
const hotSearchTags = ref([
  '温柔', '善良', '有趣', '阳光', '可爱', '幽默',
  '成熟', '稳重', '浪漫', '贴心', '上进', '孝顺'
])

// 推荐用户
const recommendedUsers = ref([
  {
    id: 'rec1',
    name: '小雨',
    age: 25,
    avatar: 'https://picsum.photos/200/200?random=10'
  },
  {
    id: 'rec2',
    name: '阳光',
    age: 28,
    avatar: 'https://picsum.photos/200/200?random=11'
  },
  {
    id: 'rec3',
    name: '微笑',
    age: 26,
    avatar: 'https://picsum.photos/200/200?random=12'
  }
])

// 排序选项
const sortOptions = ref([
  { label: '距离优先', value: 'distance' },
  { label: '活跃度', value: 'activity' },
  { label: '注册时间', value: 'register' }
])

// 筛选条件
const filters = reactive({
  minAgeIndex: 2, // 对应22岁
  maxAgeIndex: 12, // 对应32岁
  distance: 10,
  education: '不限',
  onlineOnly: false
})

// 年龄选项
const ageOptions = ref([
  '18岁', '19岁', '20岁', '21岁', '22岁', '23岁', '24岁', '25岁',
  '26岁', '27岁', '28岁', '29岁', '30岁', '31岁', '32岁', '33岁',
  '34岁', '35岁', '36岁', '37岁', '38岁', '39岁', '40岁以上'
])

// 距离选项
const distanceOptions = ref([
  { label: '1km内', value: 1 },
  { label: '5km内', value: 5 },
  { label: '10km内', value: 10 },
  { label: '20km内', value: 20 },
  { label: '不限', value: 0 }
])

// 学历选项
const educationOptions = ref([
  '不限', '高中', '大专', '本科', '硕士', '博士'
])

// 方法
const goBack = () => {
  uni.navigateBack()
}

const onSearchInput = (e) => {
  searchKeyword.value = e.detail.value
}

const clearSearch = () => {
  searchKeyword.value = ''
  hasSearched.value = false
  searchResults.value = []
}

const performSearch = () => {
  if (!searchKeyword.value.trim()) return
  
  // 添加到搜索历史
  addToHistory(searchKeyword.value)
  
  hasSearched.value = true
  pagingRef.value?.reload()
}

const searchByTag = (tag) => {
  searchKeyword.value = tag
  performSearch()
}

const searchByHistory = (keyword) => {
  searchKeyword.value = keyword
  performSearch()
}

const addToHistory = (keyword) => {
  const index = searchHistory.value.indexOf(keyword)
  if (index > -1) {
    searchHistory.value.splice(index, 1)
  }
  searchHistory.value.unshift(keyword)
  if (searchHistory.value.length > 10) {
    searchHistory.value.pop()
  }
}

const clearHistory = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有搜索历史吗？',
    success: (res) => {
      if (res.confirm) {
        searchHistory.value = []
      }
    }
  })
}

const removeHistory = (index) => {
  searchHistory.value.splice(index, 1)
}

const changeSortOption = (value) => {
  currentSort.value = value
  pagingRef.value?.reload()
}

const querySearchResults = async (pageNo, pageSize) => {
  // 模拟搜索API调用
  const mockResults = Array.from({ length: pageSize }, (_, index) => ({
    id: `search_${pageNo}_${index}`,
    name: ['小雨', '阳光', '微笑', '温柔', '可爱'][Math.floor(Math.random() * 5)],
    age: 20 + Math.floor(Math.random() * 15),
    avatar: `https://picsum.photos/300/400?random=${pageNo * pageSize + index + 50}`,
    location: '北京市朝阳区',
    distance: `${Math.floor(Math.random() * 10) + 1}km`,
    tags: ['温柔', '善良', '有趣', '阳光', '可爱', '幽默'].slice(0, 3),
    introduction: '希望找到一个真心相伴的人，一起看世界的美好。',
    isOnline: Math.random() > 0.5
  }))
  
  pagingRef.value?.complete(mockResults)
}

const viewUserProfile = (user) => {
  uni.navigateTo({ url: `/pages/dating/profile?id=${user.id}` })
}

const likeUser = (userId) => {
  uni.showToast({ title: '已喜欢', icon: 'success' })
}

// 筛选相关方法
const showFilterModal = () => {
  filterPopup.value?.open()
}

const closeFilterModal = () => {
  filterPopup.value?.close()
}

const resetFilters = () => {
  filters.minAgeIndex = 2
  filters.maxAgeIndex = 12
  filters.distance = 10
  filters.education = '不限'
  filters.onlineOnly = false
}

const onMinAgeChange = (e) => {
  filters.minAgeIndex = e.detail.value
}

const onMaxAgeChange = (e) => {
  filters.maxAgeIndex = e.detail.value
}

const selectDistance = (value) => {
  filters.distance = value
}

const selectEducation = (value) => {
  filters.education = value
}

const toggleOnlineOnly = (e) => {
  filters.onlineOnly = e.detail.value
}

const applyFilters = () => {
  closeFilterModal()
  if (hasSearched.value) {
    pagingRef.value?.reload()
  }
  uni.showToast({ title: '筛选条件已应用', icon: 'success' })
}

onMounted(() => {
  // 页面初始化
})
</script>

<style lang="scss" scoped>
.search-page {
  min-height: 100vh;
  background: #f8f9fa;
}

// 搜索栏
.search-container {
  flex: 1;
  padding: 0 20rpx;
}

.search-input-wrapper {
  background: #f8f9fa;
  border-radius: 50rpx;
  padding: 16rpx 24rpx;
  display: flex;
  align-items: center;
}

.search-icon {
  font-size: 32rpx;
  color: #95a5a6;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #2c3e50;
}

.clear-icon {
  font-size: 28rpx;
  color: #95a5a6;
  padding: 8rpx;
}

.nav-right {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
}

.filter-text {
  font-size: 28rpx;
  color: #667eea;
  margin-right: 8rpx;
}

.filter-icon {
  font-size: 28rpx;
  color: #667eea;
}

// 搜索内容
.search-content {
  padding-top: 88rpx;
}

// 搜索建议
.search-suggestions {
  padding: 30rpx;
}

.suggestion-section {
  margin-bottom: 40rpx;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-icon {
  font-size: 32rpx;
  color: #667eea;
  margin-right: 12rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  flex: 1;
}

.clear-history {
  font-size: 26rpx;
  color: #95a5a6;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag-item {
  background: #fff;
  color: #6c757d;
  padding: 16rpx 24rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
  border: 2rpx solid #ecf0f1;
  transition: all 0.3s ease;
  
  &:active {
    background: #667eea;
    color: #fff;
    border-color: #667eea;
  }
}

.history-list {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #ecf0f1;
  
  &:last-child {
    border-bottom: none;
  }
}

.history-text {
  font-size: 28rpx;
  color: #2c3e50;
  flex: 1;
}

.remove-icon {
  font-size: 24rpx;
  color: #95a5a6;
  padding: 8rpx;
}

.recommended-users {
  display: flex;
  gap: 20rpx;
  overflow-x: auto;
}

.user-card {
  flex: 0 0 120rpx;
  text-align: center;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-bottom: 12rpx;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.user-name {
  font-size: 24rpx;
  color: #2c3e50;
  margin-bottom: 4rpx;
}

.user-age {
  font-size: 22rpx;
  color: #95a5a6;
}

// 搜索结果
.search-results {
  padding: 30rpx;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.result-count {
  font-size: 28rpx;
  color: #6c757d;
}

.sort-options {
  display: flex;
  gap: 24rpx;
}

.sort-option {
  font-size: 26rpx;
  color: #95a5a6;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  
  &.active {
    background: #667eea;
    color: #fff;
  }
}

.results-container {
  margin-top: 20rpx;
}

.result-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.result-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  margin-right: 24rpx;
}

.result-info {
  flex: 1;
}

.result-basic {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.result-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-right: 12rpx;
}

.result-age {
  font-size: 26rpx;
  color: #7f8c8d;
  margin-right: 12rpx;
}

.online-dot {
  width: 16rpx;
  height: 16rpx;
  background: #2ed573;
  border-radius: 50%;
}

.result-location {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.location-icon {
  font-size: 24rpx;
  color: #95a5a6;
  margin-right: 8rpx;
}

.location-text {
  font-size: 24rpx;
  color: #95a5a6;
}

.result-tags {
  display: flex;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.result-tag {
  background: #f8f9fa;
  color: #6c757d;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.result-intro {
  font-size: 26rpx;
  color: #6c757d;
  line-height: 1.4;
}

.result-actions {
  display: flex;
  align-items: center;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.like-btn {
  background: #ff6b6b;
  color: #fff;
}

// 空状态
.empty-state {
  text-align: center;
  padding: 120rpx 60rpx;
}

.empty-icon {
  font-size: 120rpx;
  color: #ecf0f1;
  display: block;
  margin-bottom: 24rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #6c757d;
  display: block;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #95a5a6;
  display: block;
}

// 筛选弹窗
.filter-modal {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #ecf0f1;
}

.filter-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
}

.reset-btn {
  font-size: 28rpx;
  color: #667eea;
}

.filter-content {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: 40rpx;
}

.filter-label {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 600;
  display: block;
  margin-bottom: 20rpx;
}

.age-range {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.age-picker {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 120rpx;
  font-size: 26rpx;
  color: #2c3e50;
}

.age-separator {
  font-size: 26rpx;
  color: #6c757d;
}

.distance-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.distance-option {
  background: #f8f9fa;
  color: #6c757d;
  padding: 16rpx 24rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
  border: 2rpx solid transparent;
  
  &.active {
    background: #667eea;
    color: #fff;
  }
}

.education-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.education-option {
  background: #f8f9fa;
  color: #6c757d;
  padding: 16rpx 24rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
  border: 2rpx solid transparent;
  
  &.active {
    background: #667eea;
    color: #fff;
  }
}

.filter-switch {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch-label {
  font-size: 28rpx;
  color: #2c3e50;
}

.filter-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #ecf0f1;
}

.filter-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
}

.cancel-btn {
  background: #ecf0f1;
  color: #6c757d;
}

.confirm-btn {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: #fff;
}
</style>