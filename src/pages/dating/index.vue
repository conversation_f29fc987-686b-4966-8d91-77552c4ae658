<template>
  <view class="dating-page">
    <!-- 自定义导航栏 -->
    <uni-nav-bar
      :border="false"
      fixed
      background-color="transparent"
      :status-bar="true"
    >
      <template #default>
        <view class="nav-content">
          <view class="location-info" @tap="openCityPicker">
            <text class="i-carbon-location location-icon"></text>
            <text class="location-text">{{ currentCity }}</text>
            <text class="i-carbon-chevron-down chevron-icon"></text>
          </view>
          <view class="nav-actions">
            <text class="i-carbon-search nav-icon" @tap="openSearch"></text>
            <view class="notification-wrapper" @tap="openNotification">
              <text class="i-carbon-notification nav-icon"></text>
              <view v-if="hasNotification" class="notification-dot"></view>
            </view>
          </view>
        </view>
      </template>
    </uni-nav-bar>

    <!-- 内容区域 -->
    <view class="page-content">
      <!-- 底部导航栏 -->
      <view class="fixed-tabbar">
        <view
          v-for="(tab, index) in tabBarList"
          :key="index"
          class="tab-item"
          :class="{ active: currentTab === index }"
          @tap="handleTabChange(index)"
        >
          <text class="tab-icon" :class="tab.iconClass"></text>
          <text class="tab-text">{{ tab.text }}</text>
        </view>
      </view>

      <!-- 推荐页面 -->
      <view v-if="currentTab === 0" class="tab-content">
        <!-- 快捷操作区域 -->
        <view class="quick-actions-section">
          <view class="section-header">
            <text class="section-title">发现心动</text>
            <text class="section-subtitle">找到你的完美匹配</text>
          </view>
          <view class="quick-actions">
            <DatingButton
              type="primary"
              variant="gradient"
              size="lg"
              shape="pill"
              icon="i-carbon-favorite"
              text="开始匹配"
              shadow
              @click="goToMatch"
            />
            <DatingButton
              type="secondary"
              variant="outline"
              size="lg"
              shape="pill"
              icon="i-carbon-search"
              text="精准搜索"
              @click="openSearch"
            />
            <DatingButton
              type="secondary"
              variant="ghost"
              size="lg"
              shape="pill"
              icon="i-carbon-location"
              text="附近的人"
              @click="viewNearby"
            />
          </view>
        </view>

        <!-- 推荐用户列表 -->
        <view class="recommend-section">
          <view class="section-header">
            <text class="section-title">为你推荐</text>
            <text class="section-subtitle"
              >{{ userList.length }}位用户在线</text
            >
          </view>

          <z-paging
            ref="pagingRef"
            v-model="userList"
            @query="queryUserList"
            :refresher-enabled="true"
            :auto-show-back-to-top="true"
          >
            <view class="recommend-cards">
              <DatingUserCard
                v-for="user in userList"
                :key="user.id"
                :user="user"
                variant="default"
                :show-actions="true"
                :show-introduction="true"
                :max-tags="3"
                @click="viewUserDetail"
                @like="likeUser"
                @dislike="dislikeUser"
                @chat="startChat"
              />
            </view>
          </z-paging>
        </view>
      </view>

      <!-- 喜欢页面 -->
      <view v-if="currentTab === 1" class="tab-content">
        <view class="likes-header">
          <text class="likes-title">互相喜欢</text>
          <text class="likes-count">{{ likesList.length }}人</text>
        </view>
        <view class="likes-grid">
          <view
            v-for="user in likesList"
            :key="user.id"
            class="like-card"
            @tap="viewUserDetail(user)"
          >
            <image :src="user.avatar" mode="aspectFill" class="like-avatar" />
            <view class="like-info">
              <text class="like-name">{{ user.name }}</text>
              <text class="like-age">{{ user.age }}岁</text>
            </view>
            <view class="like-actions">
              <view class="chat-btn" @tap.stop="startChat(user.id)">
                <text class="i-carbon-chat"></text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 动态广场页面 -->
      <view v-if="currentTab === 2" class="tab-content">
        <view class="moments-header">
          <text class="moments-title">动态广场</text>
          <view class="publish-btn" @tap="publishMoment">
            <text class="i-carbon-add"></text>
            <text>发布</text>
          </view>
        </view>
        <view class="moments-list">
          <view
            v-for="moment in momentsList"
            :key="moment.id"
            class="moment-item"
          >
            <view class="moment-header">
              <image
                :src="moment.avatar"
                class="moment-avatar"
                @tap="viewUserDetail(moment.user)"
              />
              <view class="moment-user-info">
                <text class="moment-username">{{ moment.username }}</text>
                <text class="moment-time">{{ moment.time }}</text>
              </view>
            </view>
            <view class="moment-content">
              <text class="moment-text">{{ moment.content }}</text>
              <view v-if="moment.images" class="moment-images">
                <image
                  v-for="(img, index) in moment.images"
                  :key="index"
                  :src="img"
                  mode="aspectFill"
                  class="moment-image"
                />
              </view>
            </view>
            <view class="moment-actions">
              <view class="moment-action" @tap="likeMoment(moment.id)">
                <text class="i-carbon-favorite"></text>
                <text>{{ moment.likes }}</text>
              </view>
              <view class="moment-action" @tap="commentMoment(moment.id)">
                <text class="i-carbon-chat"></text>
                <text>{{ moment.comments }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 消息页面 -->
      <view v-if="currentTab === 3" class="tab-content">
        <view class="message-header">
          <text class="message-title">消息</text>
          <view class="message-filter">
            <text
              class="filter-item"
              :class="{ active: messageFilter === 'all' }"
              @tap="setMessageFilter('all')"
              >全部</text
            >
            <text
              class="filter-item"
              :class="{ active: messageFilter === 'unread' }"
              @tap="setMessageFilter('unread')"
              >未读</text
            >
          </view>
        </view>
        <view class="message-list">
          <view
            v-for="message in filteredMessages"
            :key="message.id"
            class="message-item"
            @tap="openChat(message)"
          >
            <view class="message-avatar-container">
              <image :src="message.avatar" class="message-avatar" />
              <view v-if="message.isOnline" class="online-dot"></view>
            </view>
            <view class="message-content">
              <view class="message-header-info">
                <text class="message-name">{{ message.name }}</text>
                <text class="message-time">{{ message.time }}</text>
              </view>
              <text class="message-last">{{ message.lastMessage }}</text>
            </view>
            <view v-if="message.unread" class="message-badge">
              {{ message.unread }}
            </view>
          </view>
        </view>
      </view>

      <!-- 个人中心页面 -->
      <view v-if="currentTab === 4" class="tab-content">
        <view class="profile-header">
          <view class="profile-avatar-section">
            <image
              :src="userProfile.avatar"
              class="profile-avatar"
              @tap="editAvatar"
            />
            <view class="profile-edit-btn" @tap="editProfile">
              <text class="i-carbon-edit"></text>
            </view>
          </view>
          <view class="profile-info">
            <text class="profile-name">{{ userProfile.name }}</text>
            <text class="profile-age">{{ userProfile.age }}岁</text>
            <view class="profile-location">
              <text class="i-carbon-location"></text>
              <text>{{ userProfile.location }}</text>
            </view>
          </view>
        </view>

        <view class="profile-stats">
          <view class="stat-item">
            <text class="stat-number">{{ userProfile.likes }}</text>
            <text class="stat-label">获得喜欢</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ userProfile.matches }}</text>
            <text class="stat-label">匹配成功</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{ userProfile.visitors }}</text>
            <text class="stat-label">访客</text>
          </view>
        </view>

        <view class="profile-menu">
          <view class="menu-item" @tap="goToSettings">
            <text class="i-carbon-settings menu-icon"></text>
            <text class="menu-text">设置</text>
            <text class="i-carbon-chevron-right menu-arrow"></text>
          </view>
          <view class="menu-item" @tap="goToVip">
            <text class="i-carbon-star menu-icon"></text>
            <text class="menu-text">VIP会员</text>
            <text class="i-carbon-chevron-right menu-arrow"></text>
          </view>
          <view class="menu-item" @tap="goToHelp">
            <text class="i-carbon-help menu-icon"></text>
            <text class="menu-text">帮助中心</text>
            <text class="i-carbon-chevron-right menu-arrow"></text>
          </view>
          <view class="menu-item" @tap="goToAbout">
            <text class="i-carbon-information menu-icon"></text>
            <text class="menu-text">关于我们</text>
            <text class="i-carbon-chevron-right menu-arrow"></text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import DatingButton from "@/components/dating/DatingButton.vue";
import DatingUserCard from "@/components/dating/DatingUserCard.vue";

// 响应式数据
const currentCity = ref("北京");
const hasNotification = ref(true);
const currentTab = ref(0);

// 数据列表
const userList = ref([]);
const likesList = ref([]);
const momentsList = ref([]);
const messageList = ref([]);
const messageFilter = ref("all");
const userProfile = ref({
  avatar: "/static/images/avatar.jpg",
  name: "小美",
  age: 25,
  location: "北京市朝阳区",
  likes: 128,
  matches: 32,
  visitors: 256,
});

// TabBar配置
const tabBarList = ref([
  {
    text: "推荐",
    iconClass: "i-carbon-favorite",
  },
  {
    text: "喜欢",
    iconClass: "i-carbon-favorite-filled",
  },
  {
    text: "动态广场",
    iconClass: "i-carbon-earth",
  },
  {
    text: "消息",
    iconClass: "i-carbon-chat",
  },
  {
    text: "个人中心",
    iconClass: "i-carbon-user",
  },
]);

// z-paging引用
const pagingRef = ref(null);

// 计算属性
const filteredMessages = computed(() => {
  if (messageFilter.value === "unread") {
    return messageList.value.filter((msg) => msg.unread > 0);
  }
  return messageList.value;
});

// 方法
const openCityPicker = () => {
  uni.showToast({ title: "选择城市", icon: "none" });
};

const openNotification = () => {
  uni.navigateTo({ url: "/pages/dating/notification" });
};

const openSearch = () => {
  uni.navigateTo({ url: "/pages/dating/search" });
};

// 切换标签页
const handleTabChange = (index: number) => {
  currentTab.value = index;
  // 根据不同标签页加载对应数据
  switch (index) {
    case 0:
      queryUserList();
      break;
    case 1:
      queryLikesList();
      break;
    case 2:
      queryMomentsList();
      break;
    case 3:
      queryMessageList();
      break;
    case 4:
      // 个人中心不需要额外加载
      break;
  }
};

// 查询用户列表
const queryUserList = async (pageNo = 1, pageSize = 10) => {
  // 模拟数据
  const mockUsers = Array.from({ length: pageSize }, (_, index) => ({
    id: `user_${pageNo}_${index}`,
    name: ["小美", "小丽", "小芳", "小慧", "小雅"][
      Math.floor(Math.random() * 5)
    ],
    age: 22 + Math.floor(Math.random() * 12),
    avatar: `https://picsum.photos/400/400?random=${pageNo * pageSize + index}`,
    distance: `${Math.floor(Math.random() * 5) + 0.5}km`,
    tags: ["旅行", "美食", "摄影", "运动", "音乐", "电影"].slice(
      0,
      Math.floor(Math.random() * 3) + 2
    ),
    isOnline: Math.random() > 0.5,
    isVip: Math.random() > 0.7,
    intro: "喜欢旅行和美食，希望遇到志同道合的人一起分享生活的美好时光。",
  }));

  if (pageNo === 1) {
    userList.value = mockUsers;
  } else {
    userList.value.push(...mockUsers);
  }

  pagingRef.value?.complete(mockUsers);
};

// 查询喜欢列表
const queryLikesList = () => {
  // 模拟数据
  const mockData = [
    {
      id: 1,
      name: "小雨",
      avatar: "/static/images/avatar1.jpg",
      age: 24,
    },
    {
      id: 2,
      name: "晓晓",
      avatar: "/static/images/avatar2.jpg",
      age: 26,
    },
    {
      id: 3,
      name: "小美",
      avatar: "/static/images/avatar3.jpg",
      age: 23,
    },
  ];
  likesList.value = mockData;
};

// 查询动态列表
const queryMomentsList = () => {
  // 模拟数据
  const mockData = [
    {
      id: 1,
      username: "小雨",
      avatar: "/static/images/avatar1.jpg",
      content: "今天天气真好，出来走走~",
      images: ["/static/images/moment1.jpg"],
      time: "2小时前",
      likes: 12,
      comments: 3,
      user: { id: 1 },
    },
    {
      id: 2,
      username: "晓晓",
      avatar: "/static/images/avatar2.jpg",
      content: "分享一下今天的美食",
      images: ["/static/images/moment2.jpg", "/static/images/moment3.jpg"],
      time: "5小时前",
      likes: 25,
      comments: 8,
      user: { id: 2 },
    },
  ];
  momentsList.value = mockData;
};

// 查询消息列表
const queryMessageList = () => {
  // 模拟数据
  const mockData = [
    {
      id: 1,
      name: "小美",
      avatar: "/static/images/avatar1.jpg",
      lastMessage: "你好，很高兴认识你",
      time: "刚刚",
      unread: 2,
      isOnline: true,
    },
    {
      id: 2,
      name: "小丽",
      avatar: "/static/images/avatar2.jpg",
      lastMessage: "今天天气不错呢",
      time: "5分钟前",
      unread: 0,
      isOnline: false,
    },
    {
      id: 3,
      name: "小芳",
      avatar: "/static/images/avatar3.jpg",
      lastMessage: "有空一起喝咖啡吗",
      time: "1小时前",
      unread: 1,
      isOnline: true,
    },
  ];
  messageList.value = mockData;
};

// 用户操作
const viewUserDetail = (user: any) => {
  uni.navigateTo({ url: `/pages/dating/profile?id=${user.id}` });
};

const likeUser = (userId: string) => {
  console.log("Like user:", userId);
  uni.showToast({ title: "已喜欢", icon: "success" });
};

const dislikeUser = (userId: string) => {
  console.log("Dislike user:", userId);
  uni.showToast({ title: "已跳过", icon: "none" });
};

const openChat = (message: any) => {
  uni.navigateTo({ url: `/pages/dating/chat?id=${message.id}` });
};

// 开始聊天
const startChat = (userId: string) => {
  uni.navigateTo({
    url: `/pages/chat/index?userId=${userId}`,
  });
};

// 设置消息过滤
const setMessageFilter = (filter: string) => {
  messageFilter.value = filter;
};

// 发布动态
const publishMoment = () => {
  uni.navigateTo({
    url: "/pages/moment/publish",
  });
};

// 点赞动态
const likeMoment = (momentId: string) => {
  const moment = momentsList.value.find((m: any) => m.id === momentId);
  if (moment) {
    moment.likes++;
  }
};

// 评论动态
const commentMoment = (momentId: string) => {
  uni.navigateTo({
    url: `/pages/moment/comment?id=${momentId}`,
  });
};

// 个人中心相关方法
const editAvatar = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      userProfile.value.avatar = res.tempFilePaths[0];
    },
  });
};

const editProfile = () => {
  uni.navigateTo({
    url: "/pages/profile/edit",
  });
};

const goToSettings = () => {
  uni.navigateTo({
    url: "/pages/settings/index",
  });
};

const goToVip = () => {
  uni.navigateTo({
    url: "/pages/vip/index",
  });
};

const goToHelp = () => {
  uni.navigateTo({
    url: "/pages/help/index",
  });
};

const goToAbout = () => {
  uni.navigateTo({
    url: "/pages/about/index",
  });
};

// 快捷操作方法
const goToMatch = () => {
  uni.navigateTo({
    url: "/pages/match/index",
  });
};

const viewNearby = () => {
  uni.navigateTo({
    url: "/pages/nearby/index",
  });
};

onMounted(() => {
  // 页面初始化
  queryUserList();
  queryLikesList();
  queryMomentsList();
  queryMessageList();
});
</script>

<style lang="scss" scoped>
@import "@/components/dating/design-system.scss";

.dating-page {
  height: 100vh;
  background: $dating-bg-primary;
  display: flex;
  flex-direction: column;
}

// 自定义导航栏样式
.nav-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
}

.location-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.location-icon {
  font-size: 28rpx;
  color: #333;
}

.location-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.chevron-icon {
  font-size: 24rpx;
  color: #666;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.nav-icon {
  font-size: 32rpx;
  color: #333;
}

.notification-wrapper {
  position: relative;
}

.notification-dot {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff4757;
  border-radius: 50%;
  border: 2rpx solid #fff;
}

// 页面内容样式
.page-content {
  flex: 1;
  overflow: hidden;
  padding-top: 88rpx;
  padding-bottom: 120rpx;
}

// 固定底部导航栏
.fixed-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: $dating-white;
  border-top: 1rpx solid $dating-gray-200;
  z-index: $dating-z-fixed;
  display: flex;
  height: 120rpx;
  box-shadow: $dating-shadow-lg;
  backdrop-filter: blur(10px);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: $dating-space-2;
  transition: all $dating-transition-base;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0;
    height: 0;
    background: rgba($dating-primary, 0.1);
    border-radius: $dating-radius-full;
    transition: all $dating-transition-base;
  }

  &.active::before {
    width: 80rpx;
    height: 80rpx;
  }
}

.tab-icon {
  font-size: 44rpx;
  color: $dating-gray-500;
  transition: all $dating-transition-base;
  position: relative;
  z-index: 1;
}

.tab-text {
  font-size: $dating-font-xs;
  color: $dating-gray-500;
  transition: all $dating-transition-base;
  font-weight: $dating-font-medium;
  position: relative;
  z-index: 1;
}

.tab-item.active .tab-icon {
  color: $dating-primary;
  transform: scale(1.1);
}

.tab-item.active .tab-text {
  color: $dating-primary;
  font-weight: $dating-font-bold;
}

// 标签页内容
.tab-content {
  height: 100%;
  padding: $dating-space-5;
  overflow-y: auto;
}

// 快捷操作区域
.quick-actions-section {
  margin-bottom: $dating-space-8;
}

.section-header {
  text-align: center;
  margin-bottom: $dating-space-6;
}

.section-title {
  font-size: $dating-font-2xl;
  font-weight: $dating-font-bold;
  color: $dating-white;
  display: block;
  margin-bottom: $dating-space-2;
}

.section-subtitle {
  font-size: $dating-font-base;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: $dating-space-4;
  padding: 0 $dating-space-4;
}

// 推荐区域
.recommend-section {
  background: $dating-bg-secondary;
  border-radius: $dating-radius-2xl $dating-radius-2xl 0 0;
  padding: $dating-space-8 $dating-space-5;
  flex: 1;

  .section-header {
    text-align: left;
    margin-bottom: $dating-space-6;
  }

  .section-title {
    font-size: $dating-font-xl;
    font-weight: $dating-font-bold;
    color: $dating-gray-900;
    margin-bottom: $dating-space-1;
  }

  .section-subtitle {
    font-size: $dating-font-sm;
    color: $dating-gray-600;
  }
}

// 推荐卡片容器
.recommend-cards {
  display: flex;
  flex-direction: column;
  gap: $dating-space-5;
}

// 旧的卡片样式已被组件替代

.card-content {
  padding: 30rpx;
}

.user-basic {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 15rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.user-age {
  font-size: 28rpx;
  color: #666;
}

.user-location {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 15rpx;
}

.location-icon {
  font-size: 24rpx;
  color: #999;
}

.location-text {
  font-size: 24rpx;
  color: #999;
}

.user-tags {
  display: flex;
  gap: 10rpx;
  flex-wrap: wrap;
  margin-bottom: 15rpx;
}

.user-tag {
  background: #f0f0f0;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.user-intro {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.card-actions {
  display: flex;
  justify-content: center;
  gap: 30rpx;
  padding: 20rpx 30rpx 30rpx;
}

.action-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.9);
}

.dislike-btn {
  background: #ff6b6b;
}

.chat-btn {
  background: #4ecdc4;
}

.like-btn {
  background: #667eea;
}

.action-icon {
  font-size: 32rpx;
}

// 喜欢页面样式
.likes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.likes-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
}

.likes-count {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.likes-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.like-card {
  background: white;
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  text-align: center;
}

.like-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-bottom: 15rpx;
}

.like-info {
  margin-bottom: 15rpx;
}

.like-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.like-age {
  font-size: 24rpx;
  color: #666;
}

.like-actions {
  display: flex;
  justify-content: center;
}

.chat-btn {
  width: 60rpx;
  height: 60rpx;
  background: #4ecdc4;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28rpx;
}

// 动态广场样式
.moments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.moments-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
}

.publish-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.moments-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.moment-item {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.moment-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.moment-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.moment-user-info {
  flex: 1;
}

.moment-username {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.moment-time {
  font-size: 24rpx;
  color: #999;
}

.moment-content {
  margin-bottom: 20rpx;
}

.moment-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 15rpx;
}

.moment-images {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10rpx;
}

.moment-image {
  width: 100%;
  height: 200rpx;
  border-radius: 10rpx;
}

.moment-actions {
  display: flex;
  gap: 40rpx;
}

.moment-action {
  display: flex;
  align-items: center;
  gap: 8rpx;
  color: #999;
  font-size: 24rpx;
}

// 消息页面样式
.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.message-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
}

.message-filter {
  display: flex;
  gap: 20rpx;
}

.filter-item {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.7);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.filter-item.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-color: rgba(255, 255, 255, 0.5);
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.message-item {
  background: white;
  border-radius: 20rpx;
  padding: 25rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.message-avatar-container {
  position: relative;
  margin-right: 20rpx;
}

.message-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}

.online-dot {
  position: absolute;
  bottom: 5rpx;
  right: 5rpx;
  width: 20rpx;
  height: 20rpx;
  background: #4caf50;
  border-radius: 50%;
  border: 3rpx solid white;
}

.message-content {
  flex: 1;
}

.message-header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.message-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.message-time {
  font-size: 24rpx;
  color: #999;
}

.message-last {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.message-badge {
  background: #ff4757;
  color: white;
  border-radius: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  padding: 0 8rpx;
  margin-left: 15rpx;
}

// 个人中心样式
.profile-header {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  text-align: center;
}

.profile-avatar-section {
  position: relative;
  display: inline-block;
  margin-bottom: 20rpx;
}

.profile-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
}

.profile-edit-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 50rpx;
  height: 50rpx;
  background: #667eea;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24rpx;
  border: 3rpx solid white;
}

.profile-info {
  text-align: center;
}

.profile-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.profile-age {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 15rpx;
}

.profile-location {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  color: #999;
  font-size: 26rpx;
}

.profile-stats {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #667eea;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

.profile-menu {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f9fa;
}

.menu-icon {
  font-size: 32rpx;
  color: #667eea;
  margin-right: 20rpx;
}

.menu-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.menu-arrow {
  font-size: 24rpx;
  color: #999;
}

// 顶部渐变背景
.header-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-top: 88rpx;
  padding-bottom: 60rpx;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    bottom: -30rpx;
    left: 0;
    right: 0;
    height: 60rpx;
    background: linear-gradient(
      to bottom,
      rgba(102, 126, 234, 0.1),
      transparent
    );
  }
}

.header-content {
  padding: 0 30rpx;
}

.location-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.location-info {
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
}

.location-text {
  font-weight: 500;
}

.notification-icon {
  position: relative;
  color: rgba(255, 255, 255, 0.9);
}

.notification-dot {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 16rpx;
  height: 16rpx;
  background: #ff4757;
  border-radius: 50%;
  border: 2rpx solid #fff;
}

.main-title {
  text-align: center;
  margin-bottom: 50rpx;
}

.title-text {
  display: block;
  color: #fff;
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.subtitle-text {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}

.search-container {
  padding: 0 20rpx;
}

.search-box {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 50rpx;
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.search-icon {
  color: rgba(255, 255, 255, 0.8);
  font-size: 32rpx;
  margin-right: 16rpx;
}

.search-placeholder {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  flex: 1;
}

// 主要内容区域
.main-content {
  margin-top: -30rpx;
  position: relative;
  z-index: 2;
}

.custom-tabbar {
  background: #fff;
  margin: 0 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.content-container {
  margin-top: 30rpx;
  padding: 0 30rpx;
}

.tab-content {
  min-height: 60vh;
}

// 用户卡片样式
.user-cards-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  padding: 20rpx 0;
}

.user-card {
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.user-image-container {
  position: relative;
  height: 240rpx;
}

.user-avatar {
  width: 100%;
  height: 100%;
}

.online-status {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 20rpx;
  height: 20rpx;
  background: #2ed573;
  border-radius: 50%;
  border: 3rpx solid #fff;
}

.vip-badge {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #fff;
  padding: 8rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
}

.user-info {
  padding: 20rpx;
}

.user-basic {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-right: 12rpx;
}

.user-age {
  font-size: 26rpx;
  color: #7f8c8d;
}

.user-location {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.location-icon {
  font-size: 24rpx;
  color: #95a5a6;
  margin-right: 8rpx;
}

.location-text {
  font-size: 24rpx;
  color: #95a5a6;
}

.user-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.tag {
  background: #f8f9fa;
  color: #6c757d;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
  padding: 20rpx;
  background: #f8f9fa;
}

.action-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.9);
  }
}

.dislike-btn {
  background: #ecf0f1;
  color: #95a5a6;
}

.like-btn {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  color: #fff;
}

// 附近用户样式
.nearby-container {
  padding: 20rpx 0;
}

.nearby-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.nearby-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  margin-right: 24rpx;
}

.nearby-info {
  flex: 1;
}

.nearby-basic {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.nearby-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-right: 12rpx;
}

.nearby-age {
  font-size: 26rpx;
  color: #7f8c8d;
}

.nearby-distance {
  font-size: 24rpx;
  color: #95a5a6;
  margin-bottom: 8rpx;
}

.nearby-intro {
  font-size: 26rpx;
  color: #6c757d;
  line-height: 1.4;
}

.nearby-action {
  padding: 16rpx;
}

.action-icon {
  font-size: 32rpx;
  color: #ff6d00;
}

// 活动样式
.events-container {
  padding: 20rpx 0;
}

.event-card {
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.event-image {
  width: 100%;
  height: 300rpx;
}

.event-info {
  padding: 24rpx;
}

.event-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
}

.event-meta {
  margin-bottom: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.meta-icon {
  font-size: 24rpx;
  color: #95a5a6;
  margin-right: 12rpx;
}

.meta-text {
  font-size: 26rpx;
  color: #6c757d;
}

.event-participants {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.participants-text {
  font-size: 24rpx;
  color: #95a5a6;
}

.join-btn {
  background: linear-gradient(45deg, #ff6d00, #ff8f00);
  color: #fff;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
}

// 消息样式
.messages-container {
  padding: 20rpx 0;
}

.message-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.message-avatar-container {
  position: relative;
  margin-right: 24rpx;
}

.message-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}

.unread-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4757;
  color: #fff;
  border-radius: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  padding: 0 8rpx;
}

.message-content {
  flex: 1;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.message-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
}

.message-time {
  font-size: 24rpx;
  color: #95a5a6;
}

.message-preview {
  font-size: 26rpx;
  color: #6c757d;
  line-height: 1.4;
}
</style>
