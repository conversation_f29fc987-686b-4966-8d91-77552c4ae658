<template>
  <view class="match-page">
    <!-- 自定义导航栏 -->
    <uni-nav-bar
      :border="false"
      fixed
      background-color="transparent"
      :status-bar="true"
      left-icon="back"
      @clickLeft="goBack"
    >
      <template #default>
        <view class="nav-title">
          <text class="title-text">发现</text>
          <text class="title-subtitle">寻找你的心动</text>
        </view>
      </template>
      <template #right>
        <view class="nav-actions">
          <DatingButton
            type="ghost"
            size="sm"
            shape="pill"
            icon="i-carbon-settings"
            @click="openSettings"
          />
        </view>
      </template>
    </uni-nav-bar>

    <!-- 背景渐变 -->
    <view class="bg-gradient"></view>

    <!-- 卡片容器 -->
    <view class="cards-container">
      <!-- 无更多用户提示 -->
      <view v-if="userCards.length === 0" class="no-more-cards">
        <view class="empty-illustration">
          <text class="i-carbon-face-satisfied no-more-icon"></text>
          <text class="no-more-title">暂时没有更多用户了</text>
          <text class="no-more-desc">稍后再来看看吧，或者调整筛选条件</text>
        </view>
        <DatingButton
          type="primary"
          variant="gradient"
          size="lg"
          shape="pill"
          icon="i-carbon-restart"
          text="重新加载"
          @click="refreshCards"
        />
      </view>

      <!-- 用户卡片 -->
      <view
        v-for="(user, index) in userCards"
        :key="user.id"
        :class="[
          'user-card',
          {
            active: index === currentIndex,
            swiping: isDragging && index === currentIndex,
          },
        ]"
        :style="getCardStyle(index)"
        @touchstart="onTouchStart"
        @touchmove="onTouchMove"
        @touchend="onTouchEnd"
      >
        <!-- 卡片内容 -->
        <view class="card-content">
          <!-- 用户照片 -->
          <view class="photo-container">
            <swiper
              class="photo-swiper"
              :indicator-dots="user.photos.length > 1"
              indicator-color="rgba(255, 255, 255, 0.3)"
              indicator-active-color="#fff"
              :autoplay="false"
              :circular="false"
            >
              <swiper-item
                v-for="(photo, photoIndex) in user.photos"
                :key="photoIndex"
              >
                <image :src="photo" mode="aspectFill" class="user-photo" />
              </swiper-item>
            </swiper>

            <!-- 状态徽章 -->
            <view class="status-badges">
              <view v-if="user.isOnline" class="online-badge">
                <view class="online-dot"></view>
                <text class="online-text">在线</text>
              </view>
              <view v-if="user.isVip" class="vip-badge">
                <text class="i-carbon-star-filled vip-icon"></text>
                <text class="vip-text">VIP</text>
              </view>
              <view v-if="user.verified" class="verified-badge">
                <text class="i-carbon-checkmark-filled verified-icon"></text>
              </view>
            </view>

            <!-- 滑动提示 -->
            <view v-if="swipeDirection" :class="['swipe-hint', swipeDirection]">
              <view class="hint-content">
                <text
                  v-if="swipeDirection === 'like'"
                  class="i-carbon-favorite hint-icon"
                ></text>
                <text v-else class="i-carbon-close hint-icon"></text>
                <text class="hint-text">{{
                  swipeDirection === "like" ? "喜欢" : "跳过"
                }}</text>
              </view>
            </view>
          </view>

          <!-- 用户信息 -->
          <view class="user-info">
            <view class="basic-info">
              <text class="user-name">{{ user.name }}</text>
              <text class="user-age">{{ user.age }}岁</text>
            </view>
            <view class="location-info">
              <text class="i-carbon-location location-icon"></text>
              <text class="location-text">{{ user.location }}</text>
              <text class="distance-text">{{ user.distance }}</text>
            </view>
            <view class="tags-container">
              <DatingTag
                v-for="tag in user.tags.slice(0, 3)"
                :key="tag"
                :text="tag"
                type="default"
                variant="ghost"
                size="sm"
                shape="pill"
              />
            </view>
            <view class="intro-text">
              <text class="intro-content">{{ user.introduction }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="action-buttons">
      <DatingButton
        type="secondary"
        variant="solid"
        size="lg"
        shape="pill"
        icon="i-carbon-close"
        class="dislike-btn"
        @click="dislikeUser"
      />
      <DatingButton
        type="warning"
        variant="gradient"
        size="lg"
        shape="pill"
        icon="i-carbon-star"
        class="super-like-btn"
        @click="superLikeUser"
      />
      <DatingButton
        type="primary"
        variant="gradient"
        size="lg"
        shape="pill"
        icon="i-carbon-favorite"
        class="like-btn"
        @click="likeUser"
      />
    </view>

    <!-- 匹配成功弹窗 -->
    <uni-popup ref="matchPopup" type="center" :mask-click="false">
      <view class="match-success">
        <view class="match-animation">
          <text class="i-carbon-favorite match-heart"></text>
          <text class="match-title">匹配成功！</text>
        </view>
        <view class="match-users">
          <image
            :src="currentUser.avatar"
            mode="aspectFill"
            class="match-avatar"
          />
          <view class="match-heart-small">
            <text class="i-carbon-favorite"></text>
          </view>
          <image
            :src="matchedUser.avatar"
            mode="aspectFill"
            class="match-avatar"
          />
        </view>
        <text class="match-desc">你和{{ matchedUser.name }}互相喜欢</text>
        <view class="match-actions">
          <view class="match-btn secondary" @tap="continueMatching">
            <text class="match-btn-text">继续寻找</text>
          </view>
          <view class="match-btn primary" @tap="startChat">
            <text class="match-btn-text">开始聊天</text>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 设置弹窗 -->
    <uni-popup ref="settingsPopup" type="bottom">
      <view class="settings-panel">
        <view class="settings-header">
          <text class="settings-title">发现设置</text>
          <text
            class="i-carbon-close settings-close"
            @tap="closeSettings"
          ></text>
        </view>
        <view class="settings-content">
          <view class="setting-item">
            <text class="setting-label">年龄范围</text>
            <view class="setting-value">
              <text class="age-range"
                >{{ ageRange[0] }} - {{ ageRange[1] }}岁</text
              >
            </view>
          </view>
          <view class="setting-item">
            <text class="setting-label">距离范围</text>
            <view class="setting-value">
              <text class="distance-range">{{ distanceRange }}km内</text>
            </view>
          </view>
          <view class="setting-item">
            <text class="setting-label">只看在线用户</text>
            <switch :checked="onlineOnly" @change="toggleOnlineOnly" />
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";

// 响应式数据
const userCards = ref([]);
const currentIndex = ref(0);
const swipeDirection = ref("");
const matchPopup = ref(null);
const settingsPopup = ref(null);
const matchedUser = ref({});

// 触摸相关
const touchStartX = ref(0);
const touchStartY = ref(0);
const touchCurrentX = ref(0);
const touchCurrentY = ref(0);
const isDragging = ref(false);
const cardTransform = ref("");

// 设置相关
const ageRange = ref([18, 35]);
const distanceRange = ref(50);
const onlineOnly = ref(false);

// 当前用户信息
const currentUser = ref({
  id: "current_user",
  name: "我",
  avatar: "https://picsum.photos/200/200?random=999",
});

// 计算属性
const currentCard = computed(() => {
  return userCards.value[currentIndex.value] || null;
});

// 方法
const goBack = () => {
  uni.navigateBack();
};

const openSettings = () => {
  settingsPopup.value?.open();
};

const closeSettings = () => {
  settingsPopup.value?.close();
};

const toggleOnlineOnly = (e) => {
  onlineOnly.value = e.detail.value;
  refreshCards();
};

const getCardStyle = (index) => {
  const offset = index - currentIndex.value;
  if (offset < 0) return { display: "none" };
  if (offset > 2) return { display: "none" };

  const scale = 1 - offset * 0.05;
  const translateY = offset * 10;
  const zIndex = 100 - offset;

  let transform = `translateY(${translateY}rpx) scale(${scale})`;

  if (index === currentIndex.value && isDragging.value) {
    const deltaX = touchCurrentX.value - touchStartX.value;
    const deltaY = touchCurrentY.value - touchStartY.value;
    const rotation = deltaX * 0.1;
    transform = `translateX(${deltaX}px) translateY(${deltaY}px) rotate(${rotation}deg) scale(${scale})`;
  }

  return {
    transform,
    zIndex,
    opacity: offset > 1 ? 0.5 : 1,
  };
};

const onTouchStart = (e) => {
  if (userCards.value.length === 0) return;

  touchStartX.value = e.touches[0].clientX;
  touchStartY.value = e.touches[0].clientY;
  touchCurrentX.value = e.touches[0].clientX;
  touchCurrentY.value = e.touches[0].clientY;
  isDragging.value = true;
  swipeDirection.value = "";
};

const onTouchMove = (e) => {
  if (!isDragging.value) return;

  touchCurrentX.value = e.touches[0].clientX;
  touchCurrentY.value = e.touches[0].clientY;

  const deltaX = touchCurrentX.value - touchStartX.value;
  const deltaY = touchCurrentY.value - touchStartY.value;

  // 判断滑动方向
  if (Math.abs(deltaX) > 50) {
    if (deltaX > 0) {
      swipeDirection.value = "like";
    } else {
      swipeDirection.value = "dislike";
    }
  } else {
    swipeDirection.value = "";
  }
};

const onTouchEnd = (e) => {
  if (!isDragging.value) return;

  const deltaX = touchCurrentX.value - touchStartX.value;
  const deltaY = touchCurrentY.value - touchStartY.value;

  isDragging.value = false;
  swipeDirection.value = "";

  // 判断是否触发操作
  if (Math.abs(deltaX) > 100) {
    if (deltaX > 0) {
      likeUser();
    } else {
      dislikeUser();
    }
  }
};

const likeUser = () => {
  if (userCards.value.length === 0) return;

  const user = userCards.value[currentIndex.value];
  console.log("喜欢用户:", user.name);

  // 模拟匹配成功
  if (Math.random() > 0.7) {
    matchedUser.value = user;
    showMatchSuccess();
  }

  nextCard();
};

const dislikeUser = () => {
  if (userCards.value.length === 0) return;

  const user = userCards.value[currentIndex.value];
  console.log("跳过用户:", user.name);

  nextCard();
};

const superLikeUser = () => {
  if (userCards.value.length === 0) return;

  const user = userCards.value[currentIndex.value];
  console.log("超级喜欢用户:", user.name);

  // 超级喜欢有更高的匹配概率
  if (Math.random() > 0.5) {
    matchedUser.value = user;
    showMatchSuccess();
  }

  nextCard();
};

const nextCard = () => {
  if (currentIndex.value < userCards.value.length - 1) {
    currentIndex.value++;
  } else {
    // 没有更多卡片了
    userCards.value = [];
    currentIndex.value = 0;
  }
};

const showMatchSuccess = () => {
  uni.vibrateShort();
  matchPopup.value?.open();
};

const continueMatching = () => {
  matchPopup.value?.close();
};

const startChat = () => {
  matchPopup.value?.close();
  uni.navigateTo({ url: `/pages/dating/chat?id=${matchedUser.value.id}` });
};

const refreshCards = () => {
  loadUserCards();
};

// 加载用户卡片数据
const loadUserCards = () => {
  // 模拟用户数据
  const mockUsers = [
    {
      id: "1",
      name: "小雨",
      age: 24,
      location: "北京市朝阳区",
      distance: 2.5,
      photos: [
        "https://picsum.photos/400/600?random=1",
        "https://picsum.photos/400/600?random=2",
        "https://picsum.photos/400/600?random=3",
      ],
      tags: ["旅行爱好者", "美食达人", "健身"],
      introduction: "喜欢旅行和美食，希望找到一个有趣的灵魂一起探索世界",
      isOnline: true,
      isVip: true,
    },
    {
      id: "2",
      name: "晓雪",
      age: 26,
      location: "北京市海淀区",
      distance: 5.2,
      photos: [
        "https://picsum.photos/400/600?random=4",
        "https://picsum.photos/400/600?random=5",
      ],
      tags: ["读书", "电影", "咖啡"],
      introduction: "文艺青年，喜欢安静的午后时光",
      isOnline: false,
      isVip: false,
    },
    {
      id: "3",
      name: "梦琪",
      age: 23,
      location: "北京市西城区",
      distance: 8.1,
      photos: [
        "https://picsum.photos/400/600?random=6",
        "https://picsum.photos/400/600?random=7",
        "https://picsum.photos/400/600?random=8",
        "https://picsum.photos/400/600?random=9",
      ],
      tags: ["运动", "音乐", "摄影"],
      introduction: "热爱生活，享受每一个美好的瞬间",
      isOnline: true,
      isVip: false,
    },
    {
      id: "4",
      name: "欣怡",
      age: 25,
      location: "北京市东城区",
      distance: 3.7,
      photos: [
        "https://picsum.photos/400/600?random=10",
        "https://picsum.photos/400/600?random=11",
      ],
      tags: ["瑜伽", "烘焙", "宠物"],
      introduction: "瑜伽教练，喜欢烘焙和小动物",
      isOnline: true,
      isVip: true,
    },
    {
      id: "5",
      name: "思雨",
      age: 27,
      location: "北京市丰台区",
      distance: 12.3,
      photos: [
        "https://picsum.photos/400/600?random=12",
        "https://picsum.photos/400/600?random=13",
        "https://picsum.photos/400/600?random=14",
      ],
      tags: ["设计", "艺术", "展览"],
      introduction: "平面设计师，对美有着独特的见解",
      isOnline: false,
      isVip: false,
    },
  ];

  // 根据设置过滤用户
  let filteredUsers = mockUsers.filter((user) => {
    if (onlineOnly.value && !user.isOnline) return false;
    if (user.age < ageRange.value[0] || user.age > ageRange.value[1])
      return false;
    if (user.distance > distanceRange.value) return false;
    return true;
  });

  userCards.value = filteredUsers;
  currentIndex.value = 0;
};

onMounted(() => {
  loadUserCards();
});
</script>

<style lang="scss" scoped>
.match-page {
  height: 100vh;
  position: relative;
  overflow: hidden;
}

// 导航栏
.nav-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
}

.nav-actions {
  padding: 0 20rpx;
}

.nav-icon {
  font-size: 32rpx;
  color: #fff;
  padding: 8rpx;
}

// 背景渐变
.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: -1;
}

// 卡片容器
.cards-container {
  position: absolute;
  top: 88rpx;
  left: 30rpx;
  right: 30rpx;
  bottom: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-more-cards {
  text-align: center;
  color: #fff;
}

.no-more-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 40rpx;
  opacity: 0.8;
}

.no-more-title {
  font-size: 36rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 16rpx;
}

.no-more-desc {
  font-size: 28rpx;
  opacity: 0.8;
  display: block;
  margin-bottom: 60rpx;
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  display: inline-flex;
  align-items: center;
  gap: 12rpx;
}

.refresh-icon {
  font-size: 28rpx;
}

.refresh-text {
  font-size: 28rpx;
}

// 用户卡片
.user-card {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 24rpx;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;

  &.active {
    cursor: grab;

    &:active {
      cursor: grabbing;
    }
  }
}

.card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.photo-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.photo-swiper {
  width: 100%;
  height: 100%;
}

.user-photo {
  width: 100%;
  height: 100%;
}

.online-indicator {
  position: absolute;
  top: 30rpx;
  left: 30rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.online-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #2ecc71;
}

.online-text {
  font-size: 22rpx;
  color: #fff;
}

.vip-badge {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  background: linear-gradient(45deg, #f39c12, #e67e22);
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.vip-icon {
  font-size: 20rpx;
  color: #fff;
}

.vip-text {
  font-size: 22rpx;
  color: #fff;
  font-weight: 600;
}

.swipe-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  border-radius: 50%;
  width: 160rpx;
  height: 160rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;

  &.like {
    background: rgba(46, 204, 113, 0.9);
  }

  &.dislike {
    background: rgba(231, 76, 60, 0.9);
  }
}

.hint-icon {
  font-size: 48rpx;
  color: #fff;
}

.hint-text {
  font-size: 24rpx;
  color: #fff;
  font-weight: 600;
}

.user-info {
  padding: 40rpx 30rpx;
  background: #fff;
}

.basic-info {
  display: flex;
  align-items: baseline;
  gap: 16rpx;
  margin-bottom: 12rpx;
}

.user-name {
  font-size: 40rpx;
  font-weight: 600;
  color: #2c3e50;
}

.user-age {
  font-size: 32rpx;
  color: #6c757d;
}

.location-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 20rpx;
}

.location-icon {
  font-size: 24rpx;
  color: #6c757d;
}

.location-text {
  font-size: 26rpx;
  color: #6c757d;
  flex: 1;
}

.distance-text {
  font-size: 24rpx;
  color: #95a5a6;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.user-tag {
  background: #f8f9fa;
  color: #6c757d;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  border: 2rpx solid #ecf0f1;
}

.intro-text {
  margin-top: 8rpx;
}

.intro-content {
  font-size: 28rpx;
  color: #2c3e50;
  line-height: 1.5;
}

// 底部操作按钮
.action-buttons {
  position: absolute;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 40rpx;
}

.action-btn {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.dislike-btn {
  background: #fff;
  border: 4rpx solid #e74c3c;

  .action-icon {
    font-size: 40rpx;
    color: #e74c3c;
  }
}

.super-like-btn {
  background: #fff;
  border: 4rpx solid #3498db;

  .action-icon {
    font-size: 36rpx;
    color: #3498db;
  }
}

.like-btn {
  background: #fff;
  border: 4rpx solid #2ecc71;

  .action-icon {
    font-size: 40rpx;
    color: #2ecc71;
  }
}

// 匹配成功弹窗
.match-success {
  background: #fff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  width: 600rpx;
  max-width: 90vw;
}

.match-animation {
  margin-bottom: 40rpx;
}

.match-heart {
  font-size: 120rpx;
  color: #e74c3c;
  display: block;
  margin-bottom: 20rpx;
  animation: heartbeat 1s infinite;
}

@keyframes heartbeat {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.match-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #2c3e50;
  display: block;
}

.match-users {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30rpx;
  margin: 40rpx 0;
}

.match-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 6rpx solid #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.match-heart-small {
  font-size: 32rpx;
  color: #e74c3c;
}

.match-desc {
  font-size: 28rpx;
  color: #6c757d;
  margin-bottom: 40rpx;
  display: block;
}

.match-actions {
  display: flex;
  gap: 20rpx;
}

.match-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 16rpx;
  text-align: center;

  &.secondary {
    background: #f8f9fa;
    border: 2rpx solid #ecf0f1;

    .match-btn-text {
      color: #6c757d;
    }
  }

  &.primary {
    background: linear-gradient(45deg, #667eea, #764ba2);

    .match-btn-text {
      color: #fff;
    }
  }
}

.match-btn-text {
  font-size: 28rpx;
  font-weight: 600;
}

// 设置弹窗
.settings-panel {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 0;
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #ecf0f1;
}

.settings-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
}

.settings-close {
  font-size: 32rpx;
  color: #6c757d;
  padding: 8rpx;
}

.settings-content {
  padding: 30rpx;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f8f9fa;

  &:last-child {
    border-bottom: none;
  }
}

.setting-label {
  font-size: 28rpx;
  color: #2c3e50;
}

.setting-value {
  display: flex;
  align-items: center;
}

.age-range,
.distance-range {
  font-size: 26rpx;
  color: #6c757d;
}
</style>
