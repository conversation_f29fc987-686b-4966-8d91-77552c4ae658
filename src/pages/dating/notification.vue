<template>
  <view class="notification-page">
    <!-- 自定义导航栏 -->
    <uni-nav-bar
      :border="false"
      fixed
      background-color="#fff"
      :status-bar="true"
      left-icon="back"
      title="消息通知"
      @clickLeft="goBack"
    >
      <template #right>
        <view class="nav-actions">
          <text class="clear-all" @tap="clearAllNotifications">清空</text>
        </view>
      </template>
    </uni-nav-bar>

    <!-- 通知内容 -->
    <view class="notification-content">
      <!-- 通知列表 -->
      <z-paging
        ref="pagingRef"
        v-model="notificationList"
        @query="queryNotifications"
        :refresher-enabled="true"
        :auto-show-back-to-top="true"
      >
        <view class="notifications-container">
          <view
            v-for="notification in notificationList"
            :key="notification.id"
            :class="['notification-item', { unread: !notification.isRead }]"
            @tap="handleNotificationTap(notification)"
          >
            <!-- 通知图标 -->
            <view class="notification-icon">
              <view :class="getNotificationIconClass(notification.type)">
                <text :class="getNotificationIcon(notification.type)"></text>
              </view>
              <view v-if="!notification.isRead" class="unread-dot"></view>
            </view>

            <!-- 通知内容 -->
            <view class="notification-content-area">
              <view class="notification-header">
                <text class="notification-title">{{ notification.title }}</text>
                <text class="notification-time">{{ formatTime(notification.time) }}</text>
              </view>
              <text class="notification-message">{{ notification.message }}</text>
              
              <!-- 用户头像（如果是用户相关通知） -->
              <view v-if="notification.userAvatar" class="notification-user">
                <image :src="notification.userAvatar" mode="aspectFill" class="user-avatar" />
                <text class="user-name">{{ notification.userName }}</text>
              </view>

              <!-- 操作按钮 -->
              <view v-if="notification.actions" class="notification-actions">
                <view
                  v-for="action in notification.actions"
                  :key="action.type"
                  :class="['action-btn', action.type]"
                  @tap.stop="handleAction(notification, action)"
                >
                  <text>{{ action.text }}</text>
                </view>
              </view>
            </view>

            <!-- 删除按钮 -->
            <view class="notification-delete" @tap.stop="deleteNotification(notification.id)">
              <text class="i-carbon-close delete-icon"></text>
            </view>
          </view>
        </view>
      </z-paging>

      <!-- 空状态 -->
      <view v-if="notificationList.length === 0" class="empty-state">
        <text class="i-carbon-notification-off empty-icon"></text>
        <text class="empty-title">暂无通知</text>
        <text class="empty-desc">当有新消息时会在这里显示</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 响应式数据
const notificationList = ref([])
const pagingRef = ref(null)

// 通知类型映射
const notificationTypes = {
  like: { icon: 'i-carbon-favorite', color: '#ff6b6b' },
  message: { icon: 'i-carbon-chat', color: '#667eea' },
  match: { icon: 'i-carbon-favorite-filled', color: '#ff6d00' },
  system: { icon: 'i-carbon-information', color: '#3498db' },
  gift: { icon: 'i-carbon-gift', color: '#e74c3c' },
  visit: { icon: 'i-carbon-view', color: '#2ecc71' }
}

// 方法
const goBack = () => {
  uni.navigateBack()
}

const queryNotifications = async (pageNo, pageSize) => {
  // 模拟API调用
  const mockNotifications = [
    {
      id: 'notif_1',
      type: 'like',
      title: '有人喜欢了你',
      message: '小雨喜欢了你，快去看看吧！',
      time: new Date(Date.now() - 5 * 60 * 1000), // 5分钟前
      isRead: false,
      userAvatar: 'https://picsum.photos/200/200?random=20',
      userName: '小雨',
      actions: [
        { type: 'primary', text: '查看资料' },
        { type: 'secondary', text: '回应' }
      ]
    },
    {
      id: 'notif_2',
      type: 'message',
      title: '新消息',
      message: '阳光给你发了一条消息',
      time: new Date(Date.now() - 30 * 60 * 1000), // 30分钟前
      isRead: false,
      userAvatar: 'https://picsum.photos/200/200?random=21',
      userName: '阳光',
      actions: [
        { type: 'primary', text: '回复' }
      ]
    },
    {
      id: 'notif_3',
      type: 'match',
      title: '恭喜匹配成功！',
      message: '你和微笑互相喜欢，现在可以开始聊天了',
      time: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2小时前
      isRead: true,
      userAvatar: 'https://picsum.photos/200/200?random=22',
      userName: '微笑',
      actions: [
        { type: 'primary', text: '开始聊天' }
      ]
    },
    {
      id: 'notif_4',
      type: 'gift',
      title: '收到礼物',
      message: '温柔送了你一朵玫瑰花',
      time: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4小时前
      isRead: true,
      userAvatar: 'https://picsum.photos/200/200?random=23',
      userName: '温柔',
      actions: [
        { type: 'primary', text: '查看礼物' },
        { type: 'secondary', text: '回礼' }
      ]
    },
    {
      id: 'notif_5',
      type: 'visit',
      title: '有人查看了你的资料',
      message: '可爱查看了你的资料',
      time: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6小时前
      isRead: true,
      userAvatar: 'https://picsum.photos/200/200?random=24',
      userName: '可爱'
    },
    {
      id: 'notif_6',
      type: 'system',
      title: '系统通知',
      message: '你的资料完整度已达到90%，完善资料可以获得更多关注',
      time: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1天前
      isRead: true
    }
  ]
  
  // 根据页码返回数据
  const startIndex = (pageNo - 1) * pageSize
  const endIndex = startIndex + pageSize
  const pageData = mockNotifications.slice(startIndex, endIndex)
  
  pagingRef.value?.complete(pageData)
}

const getNotificationIcon = (type) => {
  return notificationTypes[type]?.icon || 'i-carbon-information'
}

const getNotificationIconClass = (type) => {
  const config = notificationTypes[type] || notificationTypes.system
  return {
    'notification-icon-wrapper': true,
    [`icon-${type}`]: true
  }
}

const formatTime = (time) => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) {
    return '刚刚'
  } else if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return time.toLocaleDateString()
  }
}

const handleNotificationTap = (notification) => {
  // 标记为已读
  if (!notification.isRead) {
    notification.isRead = true
  }
  
  // 根据通知类型跳转
  switch (notification.type) {
    case 'like':
    case 'visit':
      uni.navigateTo({ url: `/pages/dating/profile?id=${notification.userId}` })
      break
    case 'message':
      uni.navigateTo({ url: `/pages/dating/chat?id=${notification.userId}` })
      break
    case 'match':
      uni.navigateTo({ url: `/pages/dating/chat?id=${notification.userId}` })
      break
    case 'gift':
      uni.navigateTo({ url: `/pages/dating/gift?id=${notification.userId}` })
      break
    case 'system':
      // 系统通知可能跳转到设置或其他页面
      break
  }
}

const handleAction = (notification, action) => {
  switch (action.type) {
    case 'primary':
      if (notification.type === 'like') {
        uni.navigateTo({ url: `/pages/dating/profile?id=${notification.userId}` })
      } else if (notification.type === 'message') {
        uni.navigateTo({ url: `/pages/dating/chat?id=${notification.userId}` })
      } else if (notification.type === 'match') {
        uni.navigateTo({ url: `/pages/dating/chat?id=${notification.userId}` })
      } else if (notification.type === 'gift') {
        uni.showToast({ title: '查看礼物', icon: 'none' })
      }
      break
    case 'secondary':
      if (notification.type === 'like') {
        uni.showToast({ title: '回应成功', icon: 'success' })
      } else if (notification.type === 'gift') {
        uni.showToast({ title: '回礼功能开发中', icon: 'none' })
      }
      break
  }
}

const deleteNotification = (notificationId) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这条通知吗？',
    success: (res) => {
      if (res.confirm) {
        const index = notificationList.value.findIndex(item => item.id === notificationId)
        if (index > -1) {
          notificationList.value.splice(index, 1)
          uni.showToast({ title: '删除成功', icon: 'success' })
        }
      }
    }
  })
}

const clearAllNotifications = () => {
  if (notificationList.value.length === 0) {
    uni.showToast({ title: '暂无通知可清空', icon: 'none' })
    return
  }
  
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有通知吗？',
    success: (res) => {
      if (res.confirm) {
        notificationList.value = []
        uni.showToast({ title: '清空成功', icon: 'success' })
      }
    }
  })
}

onMounted(() => {
  // 页面初始化
})
</script>

<style lang="scss" scoped>
.notification-page {
  min-height: 100vh;
  background: #f8f9fa;
}

.nav-actions {
  padding: 0 20rpx;
}

.clear-all {
  font-size: 28rpx;
  color: #667eea;
}

.notification-content {
  padding-top: 88rpx;
}

.notifications-container {
  padding: 20rpx 30rpx;
}

.notification-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  position: relative;
  transition: all 0.3s ease;
  
  &.unread {
    border-left: 6rpx solid #667eea;
    background: #f8f9ff;
  }
  
  &:active {
    transform: scale(0.98);
  }
}

.notification-icon {
  position: relative;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.notification-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #fff;
  
  &.icon-like {
    background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  }
  
  &.icon-message {
    background: linear-gradient(45deg, #667eea, #764ba2);
  }
  
  &.icon-match {
    background: linear-gradient(45deg, #ff6d00, #ff8f00);
  }
  
  &.icon-system {
    background: linear-gradient(45deg, #3498db, #5dade2);
  }
  
  &.icon-gift {
    background: linear-gradient(45deg, #e74c3c, #ec7063);
  }
  
  &.icon-visit {
    background: linear-gradient(45deg, #2ecc71, #58d68d);
  }
}

.unread-dot {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 20rpx;
  height: 20rpx;
  background: #ff4757;
  border-radius: 50%;
  border: 3rpx solid #fff;
}

.notification-content-area {
  flex: 1;
  min-width: 0;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8rpx;
}

.notification-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
  flex: 1;
  margin-right: 16rpx;
}

.notification-time {
  font-size: 24rpx;
  color: #95a5a6;
  flex-shrink: 0;
}

.notification-message {
  font-size: 26rpx;
  color: #6c757d;
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: block;
}

.notification-user {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}

.user-name {
  font-size: 26rpx;
  color: #2c3e50;
  font-weight: 500;
}

.notification-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 16rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  text-align: center;
  transition: all 0.3s ease;
  
  &.primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: #fff;
  }
  
  &.secondary {
    background: #f8f9fa;
    color: #6c757d;
    border: 2rpx solid #ecf0f1;
  }
  
  &:active {
    transform: scale(0.95);
  }
}

.notification-delete {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  
  &:active {
    background: rgba(0, 0, 0, 0.1);
    transform: scale(0.9);
  }
}

.delete-icon {
  font-size: 24rpx;
  color: #95a5a6;
}

// 空状态
.empty-state {
  text-align: center;
  padding: 120rpx 60rpx;
}

.empty-icon {
  font-size: 120rpx;
  color: #ecf0f1;
  display: block;
  margin-bottom: 24rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #6c757d;
  display: block;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #95a5a6;
  display: block;
}
</style>