<template>
  <view class="chat-page">
    <!-- 自定义导航栏 -->
    <uni-nav-bar
      :border="false"
      fixed
      background-color="#fff"
      :status-bar="true"
      left-icon="back"
      @clickLeft="goBack"
    >
      <template #default>
        <view class="chat-header" @tap="viewUserProfile">
          <image :src="chatUser.avatar" mode="aspectFill" class="header-avatar" />
          <view class="header-info">
            <text class="header-name">{{ chatUser.name }}</text>
            <text class="header-status">{{ chatUser.isOnline ? '在线' : chatUser.lastSeen }}</text>
          </view>
        </view>
      </template>
      <template #right>
        <view class="nav-actions">
          <text class="i-carbon-phone nav-icon" @tap="makeCall"></text>
          <text class="i-carbon-video nav-icon" @tap="makeVideoCall"></text>
          <text class="i-carbon-overflow-menu-horizontal nav-icon" @tap="showMoreActions"></text>
        </view>
      </template>
    </uni-nav-bar>

    <!-- 聊天消息区域 -->
    <scroll-view
      class="chat-messages"
      scroll-y
      :scroll-top="scrollTop"
      :scroll-with-animation="true"
      @scrolltoupper="loadMoreMessages"
    >
      <!-- 加载更多提示 -->
      <view v-if="isLoadingMore" class="loading-more">
        <uni-load-more status="loading" content-text="{ contentText: { contentdown: '加载更多', contentrefresh: '加载中...', contentnomore: '没有更多了' } }"></uni-load-more>
      </view>

      <!-- 消息列表 -->
      <view class="messages-container">
        <view
          v-for="(message, index) in messageList"
          :key="message.id"
          :class="['message-item', message.type]"
        >
          <!-- 时间分隔线 -->
          <view v-if="shouldShowTime(index)" class="time-divider">
            <text class="time-text">{{ formatMessageTime(message.timestamp) }}</text>
          </view>

          <!-- 系统消息 -->
          <view v-if="message.type === 'system'" class="system-message">
            <text class="system-text">{{ message.content }}</text>
          </view>

          <!-- 普通消息 -->
          <view v-else class="normal-message">
            <!-- 头像（对方消息才显示） -->
            <image
              v-if="message.type === 'received'"
              :src="chatUser.avatar"
              mode="aspectFill"
              class="message-avatar"
              @tap="viewUserProfile"
            />

            <!-- 消息内容 -->
            <view class="message-content">
              <!-- 文本消息 -->
              <view v-if="message.messageType === 'text'" class="text-message">
                <text class="message-text">{{ message.content }}</text>
              </view>

              <!-- 图片消息 -->
              <view v-else-if="message.messageType === 'image'" class="image-message">
                <image
                  :src="message.content"
                  mode="aspectFill"
                  class="message-image"
                  @tap="previewImage(message.content)"
                />
              </view>

              <!-- 语音消息 -->
              <view v-else-if="message.messageType === 'voice'" class="voice-message" @tap="playVoice(message)">
                <text class="i-carbon-microphone voice-icon"></text>
                <text class="voice-duration">{{ message.duration }}"</text>
                <view v-if="message.isPlaying" class="voice-playing">
                  <view class="wave-item" v-for="i in 3" :key="i"></view>
                </view>
              </view>

              <!-- 礼物消息 -->
              <view v-else-if="message.messageType === 'gift'" class="gift-message">
                <image :src="message.giftImage" mode="aspectFit" class="gift-image" />
                <text class="gift-text">{{ message.content }}</text>
              </view>

              <!-- 消息状态 -->
              <view v-if="message.type === 'sent'" class="message-status">
                <text v-if="message.status === 'sending'" class="i-carbon-time status-icon sending"></text>
                <text v-else-if="message.status === 'sent'" class="i-carbon-checkmark status-icon sent"></text>
                <text v-else-if="message.status === 'read'" class="i-carbon-checkmark-filled status-icon read"></text>
                <text v-else-if="message.status === 'failed'" class="i-carbon-warning status-icon failed" @tap="resendMessage(message)"></text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 输入区域 -->
    <view class="chat-input-area">
      <!-- 快捷回复 -->
      <view v-if="showQuickReplies" class="quick-replies">
        <scroll-view class="quick-scroll" scroll-x>
          <view class="quick-items">
            <text
              v-for="reply in quickReplies"
              :key="reply"
              class="quick-item"
              @tap="sendQuickReply(reply)"
            >
              {{ reply }}
            </text>
          </view>
        </scroll-view>
      </view>

      <!-- 输入框区域 -->
      <view class="input-container">
        <view class="input-wrapper">
          <!-- 语音按钮 -->
          <view class="voice-btn" @tap="toggleVoiceMode">
            <text :class="isVoiceMode ? 'i-carbon-keyboard' : 'i-carbon-microphone'" class="voice-icon"></text>
          </view>

          <!-- 文本输入 -->
          <view v-if="!isVoiceMode" class="text-input-wrapper">
            <textarea
              v-model="inputText"
              class="text-input"
              placeholder="说点什么..."
              :auto-height="true"
              :max-height="200"
              @focus="onInputFocus"
              @blur="onInputBlur"
              @input="onInputChange"
            />
          </view>

          <!-- 语音输入 -->
          <view v-else class="voice-input-wrapper">
            <view
              :class="['voice-record-btn', { recording: isRecording }]"
              @touchstart="startRecord"
              @touchend="stopRecord"
              @touchcancel="cancelRecord"
            >
              <text class="record-text">{{ isRecording ? '松开发送' : '按住说话' }}</text>
            </view>
          </view>

          <!-- 表情按钮 -->
          <view class="emoji-btn" @tap="toggleEmojiPanel">
            <text class="i-carbon-face-satisfied emoji-icon"></text>
          </view>

          <!-- 更多按钮 -->
          <view class="more-btn" @tap="toggleMorePanel">
            <text class="i-carbon-add more-icon"></text>
          </view>

          <!-- 发送按钮 -->
          <view
            v-if="inputText.trim() || isVoiceMode"
            class="send-btn"
            @tap="sendMessage"
          >
            <text class="i-carbon-send send-icon"></text>
          </view>
        </view>

        <!-- 表情面板 -->
        <view v-if="showEmojiPanel" class="emoji-panel">
          <scroll-view class="emoji-scroll" scroll-y>
            <view class="emoji-grid">
              <text
                v-for="emoji in emojiList"
                :key="emoji"
                class="emoji-item"
                @tap="insertEmoji(emoji)"
              >
                {{ emoji }}
              </text>
            </view>
          </scroll-view>
        </view>

        <!-- 更多功能面板 -->
        <view v-if="showMorePanel" class="more-panel">
          <view class="more-grid">
            <view class="more-item" @tap="selectImage">
              <view class="more-icon-wrapper">
                <text class="i-carbon-image more-item-icon"></text>
              </view>
              <text class="more-item-text">照片</text>
            </view>
            <view class="more-item" @tap="takePhoto">
              <view class="more-icon-wrapper">
                <text class="i-carbon-camera more-item-icon"></text>
              </view>
              <text class="more-item-text">拍照</text>
            </view>
            <view class="more-item" @tap="sendGift">
              <view class="more-icon-wrapper">
                <text class="i-carbon-gift more-item-icon"></text>
              </view>
              <text class="more-item-text">礼物</text>
            </view>
            <view class="more-item" @tap="sendLocation">
              <view class="more-icon-wrapper">
                <text class="i-carbon-location more-item-icon"></text>
              </view>
              <text class="more-item-text">位置</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 更多操作弹窗 -->
    <uni-popup ref="morePopup" type="bottom">
      <view class="more-actions">
        <view class="action-item" @tap="viewUserProfile">
          <text class="i-carbon-user action-item-icon"></text>
          <text class="action-item-text">查看资料</text>
        </view>
        <view class="action-item" @tap="clearChatHistory">
          <text class="i-carbon-clean action-item-icon"></text>
          <text class="action-item-text">清空聊天记录</text>
        </view>
        <view class="action-item" @tap="reportUser">
          <text class="i-carbon-warning action-item-icon"></text>
          <text class="action-item-text">举报用户</text>
        </view>
        <view class="action-item cancel-item" @tap="closeMoreActions">
          <text class="action-item-text">取消</text>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'

// 获取页面参数
const pages = getCurrentPages()
const currentPage = pages[pages.length - 1]
const userId = currentPage.options?.id || ''

// 响应式数据
const chatUser = ref({
  id: '',
  name: '',
  avatar: '',
  isOnline: false,
  lastSeen: ''
})

const messageList = ref([])
const inputText = ref('')
const scrollTop = ref(0)
const isLoadingMore = ref(false)
const isVoiceMode = ref(false)
const isRecording = ref(false)
const showEmojiPanel = ref(false)
const showMorePanel = ref(false)
const showQuickReplies = ref(true)
const morePopup = ref(null)

// 快捷回复
const quickReplies = ref([
  '你好', '在吗', '好的', '谢谢', '晚安', '😊', '👍', '❤️'
])

// 表情列表
const emojiList = ref([
  '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
  '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
  '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
  '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
  '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
  '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠',
  '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨',
  '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥',
  '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧',
  '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐'
])

// 方法
const goBack = () => {
  uni.navigateBack()
}

const viewUserProfile = () => {
  uni.navigateTo({ url: `/pages/dating/profile?id=${chatUser.value.id}` })
}

const makeCall = () => {
  uni.showToast({ title: '语音通话功能开发中', icon: 'none' })
}

const makeVideoCall = () => {
  uni.showToast({ title: '视频通话功能开发中', icon: 'none' })
}

const showMoreActions = () => {
  morePopup.value?.open()
}

const closeMoreActions = () => {
  morePopup.value?.close()
}

const loadMoreMessages = () => {
  if (isLoadingMore.value) return
  
  isLoadingMore.value = true
  // 模拟加载更多消息
  setTimeout(() => {
    isLoadingMore.value = false
  }, 1000)
}

const shouldShowTime = (index) => {
  if (index === 0) return true
  
  const current = messageList.value[index]
  const previous = messageList.value[index - 1]
  
  // 如果时间间隔超过5分钟，显示时间
  return current.timestamp - previous.timestamp > 5 * 60 * 1000
}

const formatMessageTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
  
  if (messageDate.getTime() === today.getTime()) {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else {
    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }) + ' ' +
           date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  }
}

const onInputFocus = () => {
  showEmojiPanel.value = false
  showMorePanel.value = false
  showQuickReplies.value = false
  scrollToBottom()
}

const onInputBlur = () => {
  showQuickReplies.value = true
}

const onInputChange = () => {
  // 可以在这里实现正在输入的状态
}

const toggleVoiceMode = () => {
  isVoiceMode.value = !isVoiceMode.value
  showEmojiPanel.value = false
  showMorePanel.value = false
}

const toggleEmojiPanel = () => {
  showEmojiPanel.value = !showEmojiPanel.value
  showMorePanel.value = false
  if (showEmojiPanel.value) {
    scrollToBottom()
  }
}

const toggleMorePanel = () => {
  showMorePanel.value = !showMorePanel.value
  showEmojiPanel.value = false
  if (showMorePanel.value) {
    scrollToBottom()
  }
}

const insertEmoji = (emoji) => {
  inputText.value += emoji
}

const sendQuickReply = (reply) => {
  inputText.value = reply
  sendMessage()
}

const sendMessage = () => {
  if (!inputText.value.trim()) return
  
  const message = {
    id: Date.now().toString(),
    type: 'sent',
    messageType: 'text',
    content: inputText.value.trim(),
    timestamp: Date.now(),
    status: 'sending'
  }
  
  messageList.value.push(message)
  inputText.value = ''
  scrollToBottom()
  
  // 模拟发送状态变化
  setTimeout(() => {
    message.status = 'sent'
  }, 1000)
  
  setTimeout(() => {
    message.status = 'read'
  }, 2000)
  
  // 模拟对方回复
  setTimeout(() => {
    const reply = {
      id: (Date.now() + 1).toString(),
      type: 'received',
      messageType: 'text',
      content: '收到了，谢谢！',
      timestamp: Date.now()
    }
    messageList.value.push(reply)
    scrollToBottom()
  }, 3000)
}

const startRecord = () => {
  isRecording.value = true
  uni.vibrateShort()
}

const stopRecord = () => {
  if (!isRecording.value) return
  
  isRecording.value = false
  
  // 模拟发送语音消息
  const voiceMessage = {
    id: Date.now().toString(),
    type: 'sent',
    messageType: 'voice',
    content: '语音消息',
    duration: Math.floor(Math.random() * 30) + 1,
    timestamp: Date.now(),
    status: 'sent'
  }
  
  messageList.value.push(voiceMessage)
  scrollToBottom()
}

const cancelRecord = () => {
  isRecording.value = false
}

const playVoice = (message) => {
  message.isPlaying = !message.isPlaying
  
  if (message.isPlaying) {
    setTimeout(() => {
      message.isPlaying = false
    }, message.duration * 1000)
  }
}

const previewImage = (imageUrl) => {
  uni.previewImage({
    urls: [imageUrl],
    current: imageUrl
  })
}

const selectImage = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      const imageMessage = {
        id: Date.now().toString(),
        type: 'sent',
        messageType: 'image',
        content: res.tempFilePaths[0],
        timestamp: Date.now(),
        status: 'sent'
      }
      
      messageList.value.push(imageMessage)
      scrollToBottom()
      showMorePanel.value = false
    }
  })
}

const takePhoto = () => {
  uni.chooseImage({
    count: 1,
    sourceType: ['camera'],
    success: (res) => {
      const imageMessage = {
        id: Date.now().toString(),
        type: 'sent',
        messageType: 'image',
        content: res.tempFilePaths[0],
        timestamp: Date.now(),
        status: 'sent'
      }
      
      messageList.value.push(imageMessage)
      scrollToBottom()
      showMorePanel.value = false
    }
  })
}

const sendGift = () => {
  uni.showToast({ title: '礼物功能开发中', icon: 'none' })
  showMorePanel.value = false
}

const sendLocation = () => {
  uni.showToast({ title: '位置功能开发中', icon: 'none' })
  showMorePanel.value = false
}

const resendMessage = (message) => {
  message.status = 'sending'
  
  setTimeout(() => {
    message.status = 'sent'
  }, 1000)
}

const clearChatHistory = () => {
  closeMoreActions()
  uni.showModal({
    title: '确认清空',
    content: '确定要清空聊天记录吗？',
    success: (res) => {
      if (res.confirm) {
        messageList.value = []
        uni.showToast({ title: '清空成功', icon: 'success' })
      }
    }
  })
}

const reportUser = () => {
  closeMoreActions()
  uni.showToast({ title: '举报成功', icon: 'success' })
}

const scrollToBottom = () => {
  nextTick(() => {
    scrollTop.value = 999999
  })
}

// 初始化数据
const initChatData = () => {
  // 模拟聊天用户信息
  chatUser.value = {
    id: userId,
    name: '小雨',
    avatar: 'https://picsum.photos/200/200?random=30',
    isOnline: true,
    lastSeen: '2小时前'
  }
  
  // 模拟聊天记录
  messageList.value = [
    {
      id: '1',
      type: 'system',
      content: '你们已经匹配成功，开始聊天吧！',
      timestamp: Date.now() - 24 * 60 * 60 * 1000
    },
    {
      id: '2',
      type: 'received',
      messageType: 'text',
      content: '你好，很高兴认识你！',
      timestamp: Date.now() - 2 * 60 * 60 * 1000
    },
    {
      id: '3',
      type: 'sent',
      messageType: 'text',
      content: '你好！我也很高兴认识你',
      timestamp: Date.now() - 2 * 60 * 60 * 1000 + 30000,
      status: 'read'
    },
    {
      id: '4',
      type: 'received',
      messageType: 'text',
      content: '你的照片很好看呢',
      timestamp: Date.now() - 30 * 60 * 1000
    },
    {
      id: '5',
      type: 'sent',
      messageType: 'text',
      content: '谢谢夸奖😊',
      timestamp: Date.now() - 25 * 60 * 1000,
      status: 'read'
    }
  ]
  
  scrollToBottom()
}

onMounted(() => {
  initChatData()
})
</script>

<style lang="scss" scoped>
.chat-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

// 聊天头部
.chat-header {
  display: flex;
  align-items: center;
  flex: 1;
}

.header-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.header-info {
  flex: 1;
}

.header-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  display: block;
  margin-bottom: 4rpx;
}

.header-status {
  font-size: 24rpx;
  color: #95a5a6;
  display: block;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 0 20rpx;
}

.nav-icon {
  font-size: 32rpx;
  color: #6c757d;
  padding: 8rpx;
}

// 消息区域
.chat-messages {
  flex: 1;
  padding-top: 88rpx;
  padding-bottom: 20rpx;
}

.loading-more {
  padding: 20rpx;
  text-align: center;
}

.messages-container {
  padding: 0 30rpx;
}

.message-item {
  margin-bottom: 24rpx;
}

.time-divider {
  text-align: center;
  margin: 32rpx 0;
}

.time-text {
  background: rgba(0, 0, 0, 0.1);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
}

.system-message {
  text-align: center;
}

.system-text {
  background: rgba(0, 0, 0, 0.1);
  color: #fff;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.normal-message {
  display: flex;
  align-items: flex-end;
  
  &.sent {
    flex-direction: row-reverse;
    
    .message-content {
      margin-right: 0;
      margin-left: 16rpx;
    }
  }
  
  &.received {
    .message-content {
      margin-left: 16rpx;
    }
  }
}

.message-avatar {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  flex-shrink: 0;
}

.message-content {
  max-width: 70%;
  position: relative;
}

.text-message {
  background: #fff;
  padding: 20rpx 24rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  
  .sent & {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: #fff;
  }
}

.message-text {
  font-size: 28rpx;
  line-height: 1.5;
  word-wrap: break-word;
}

.image-message {
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.message-image {
  width: 300rpx;
  height: 300rpx;
  border-radius: 16rpx;
}

.voice-message {
  background: #fff;
  padding: 20rpx 24rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  min-width: 120rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  
  .sent & {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: #fff;
  }
}

.voice-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.voice-duration {
  font-size: 24rpx;
  margin-right: 12rpx;
}

.voice-playing {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.wave-item {
  width: 4rpx;
  height: 20rpx;
  background: currentColor;
  border-radius: 2rpx;
  animation: wave 1s infinite;
  
  &:nth-child(2) {
    animation-delay: 0.1s;
  }
  
  &:nth-child(3) {
    animation-delay: 0.2s;
  }
}

@keyframes wave {
  0%, 100% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(0.5);
  }
}

.gift-message {
  background: #fff;
  padding: 20rpx;
  border-radius: 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.gift-image {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 12rpx;
}

.gift-text {
  font-size: 24rpx;
  color: #6c757d;
  display: block;
}

.message-status {
  position: absolute;
  bottom: -24rpx;
  right: 0;
}

.status-icon {
  font-size: 20rpx;
  
  &.sending {
    color: #95a5a6;
  }
  
  &.sent {
    color: #95a5a6;
  }
  
  &.read {
    color: #667eea;
  }
  
  &.failed {
    color: #e74c3c;
  }
}

// 输入区域
.chat-input-area {
  background: #fff;
  border-top: 1rpx solid #ecf0f1;
}

.quick-replies {
  padding: 20rpx 30rpx 0;
  border-bottom: 1rpx solid #ecf0f1;
}

.quick-scroll {
  white-space: nowrap;
}

.quick-items {
  display: flex;
  gap: 16rpx;
}

.quick-item {
  background: #f8f9fa;
  color: #6c757d;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  white-space: nowrap;
  border: 2rpx solid #ecf0f1;
  
  &:active {
    background: #667eea;
    color: #fff;
    border-color: #667eea;
  }
}

.input-container {
  padding: 20rpx 30rpx;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 16rpx;
}

.voice-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.voice-icon {
  font-size: 32rpx;
  color: #6c757d;
}

.text-input-wrapper {
  flex: 1;
  background: #f8f9fa;
  border-radius: 24rpx;
  padding: 16rpx 24rpx;
  min-height: 72rpx;
  display: flex;
  align-items: center;
}

.text-input {
  width: 100%;
  font-size: 28rpx;
  color: #2c3e50;
  line-height: 1.5;
}

.voice-input-wrapper {
  flex: 1;
}

.voice-record-btn {
  background: #f8f9fa;
  border-radius: 24rpx;
  padding: 24rpx;
  text-align: center;
  transition: all 0.3s ease;
  
  &.recording {
    background: #667eea;
    color: #fff;
  }
}

.record-text {
  font-size: 28rpx;
}

.emoji-btn, .more-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.emoji-icon, .more-icon {
  font-size: 32rpx;
  color: #6c757d;
}

.send-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.send-icon {
  font-size: 32rpx;
  color: #fff;
}

// 表情面板
.emoji-panel {
  height: 400rpx;
  border-top: 1rpx solid #ecf0f1;
  padding: 20rpx;
}

.emoji-scroll {
  height: 100%;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 16rpx;
}

.emoji-item {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  border-radius: 12rpx;
  
  &:active {
    background: #f8f9fa;
  }
}

// 更多功能面板
.more-panel {
  padding: 30rpx;
  border-top: 1rpx solid #ecf0f1;
}

.more-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40rpx;
}

.more-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.more-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  border-radius: 20rpx;
  background: linear-gradient(45deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
}

.more-item-icon {
  font-size: 40rpx;
  color: #fff;
}

.more-item-text {
  font-size: 24rpx;
  color: #6c757d;
}

// 更多操作弹窗
.more-actions {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #ecf0f1;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.cancel-item {
    justify-content: center;
    margin-top: 20rpx;
    background: #f8f9fa;
    border-radius: 16rpx;
    padding: 24rpx;
  }
}

.action-item-icon {
  font-size: 32rpx;
  color: #6c757d;
  margin-right: 16rpx;
}

.action-item-text {
  font-size: 30rpx;
  color: #2c3e50;
}

.cancel-item .action-item-text {
  color: #6c757d;
}
</style>