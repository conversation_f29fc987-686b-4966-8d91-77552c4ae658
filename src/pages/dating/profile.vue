<template>
  <view class="profile-page">
    <!-- 自定义导航栏 -->
    <uni-nav-bar
      :border="false"
      fixed
      background-color="transparent"
      :status-bar="true"
      left-icon="back"
      @clickLeft="goBack"
    >
      <template #right>
        <view class="nav-actions">
          <text class="i-carbon-overflow-menu-horizontal nav-icon" @tap="showMoreActions"></text>
        </view>
      </template>
    </uni-nav-bar>

    <!-- 用户头像和基本信息 -->
    <view class="profile-header">
      <view class="avatar-container">
        <swiper
          class="avatar-swiper"
          :indicator-dots="userInfo.photos.length > 1"
          :autoplay="false"
          indicator-color="rgba(255,255,255,0.5)"
          indicator-active-color="#fff"
        >
          <swiper-item v-for="(photo, index) in userInfo.photos" :key="index">
            <image :src="photo" mode="aspectFill" class="profile-avatar" />
          </swiper-item>
        </swiper>
        
        <!-- 在线状态 -->
        <view v-if="userInfo.isOnline" class="online-indicator">
          <view class="online-dot"></view>
          <text class="online-text">在线</text>
        </view>
        
        <!-- VIP标识 -->
        <view v-if="userInfo.isVip" class="vip-badge">
          <text class="i-carbon-star-filled"></text>
          <text class="vip-text">VIP</text>
        </view>
      </view>

      <!-- 基本信息 -->
      <view class="basic-info">
        <view class="name-section">
          <text class="user-name">{{ userInfo.name }}</text>
          <text class="user-age">{{ userInfo.age }}岁</text>
          <view v-if="userInfo.verified" class="verified-badge">
            <text class="i-carbon-checkmark-filled"></text>
          </view>
        </view>
        
        <view class="location-info">
          <text class="i-carbon-location location-icon"></text>
          <text class="location-text">{{ userInfo.location }} · 距离{{ userInfo.distance }}</text>
        </view>
        
        <view class="last-active">
          <text class="active-text">{{ userInfo.lastActive }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <view class="action-buttons">
        <view class="action-btn secondary-btn" @tap="sendGift">
          <text class="i-carbon-gift btn-icon"></text>
          <text class="btn-text">送礼物</text>
        </view>
        <view class="action-btn primary-btn" @tap="startChat">
          <text class="i-carbon-chat btn-icon"></text>
          <text class="btn-text">开始聊天</text>
        </view>
        <view class="action-btn like-btn" @tap="toggleLike">
          <text :class="isLiked ? 'i-carbon-favorite-filled' : 'i-carbon-favorite'" class="btn-icon"></text>
        </view>
      </view>
    </view>

    <!-- 详细信息 -->
    <view class="detail-content">
      <!-- 个人简介 -->
      <view class="info-section">
        <view class="section-title">
          <text class="i-carbon-user section-icon"></text>
          <text class="title-text">个人简介</text>
        </view>
        <view class="intro-content">
          <text class="intro-text">{{ userInfo.introduction || '这个人很神秘，什么都没有留下...' }}</text>
        </view>
      </view>

      <!-- 标签 -->
      <view class="info-section">
        <view class="section-title">
          <text class="i-carbon-tag section-icon"></text>
          <text class="title-text">个人标签</text>
        </view>
        <view class="tags-container">
          <text
            v-for="tag in userInfo.tags"
            :key="tag"
            class="tag-item"
          >
            {{ tag }}
          </text>
        </view>
      </view>

      <!-- 基本资料 -->
      <view class="info-section">
        <view class="section-title">
          <text class="i-carbon-information section-icon"></text>
          <text class="title-text">基本资料</text>
        </view>
        <view class="info-list">
          <view class="info-item">
            <text class="info-label">身高</text>
            <text class="info-value">{{ userInfo.height || '未填写' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">学历</text>
            <text class="info-value">{{ userInfo.education || '未填写' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">职业</text>
            <text class="info-value">{{ userInfo.occupation || '未填写' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">月收入</text>
            <text class="info-value">{{ userInfo.income || '未填写' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">星座</text>
            <text class="info-value">{{ userInfo.constellation || '未填写' }}</text>
          </view>
        </view>
      </view>

      <!-- 兴趣爱好 -->
      <view class="info-section">
        <view class="section-title">
          <text class="i-carbon-favorite section-icon"></text>
          <text class="title-text">兴趣爱好</text>
        </view>
        <view class="hobbies-container">
          <view
            v-for="hobby in userInfo.hobbies"
            :key="hobby"
            class="hobby-item"
          >
            <text class="hobby-text">{{ hobby }}</text>
          </view>
        </view>
      </view>

      <!-- 择偶要求 -->
      <view class="info-section">
        <view class="section-title">
          <text class="i-carbon-search section-icon"></text>
          <text class="title-text">择偶要求</text>
        </view>
        <view class="requirements-content">
          <text class="requirements-text">{{ userInfo.requirements || '缘分到了自然就遇见了' }}</text>
        </view>
      </view>
    </view>

    <!-- 举报弹窗 -->
    <uni-popup ref="morePopup" type="bottom">
      <view class="more-actions">
        <view class="action-item" @tap="reportUser">
          <text class="i-carbon-warning action-item-icon"></text>
          <text class="action-item-text">举报用户</text>
        </view>
        <view class="action-item" @tap="blockUser">
          <text class="i-carbon-user-x action-item-icon"></text>
          <text class="action-item-text">拉黑用户</text>
        </view>
        <view class="action-item cancel-item" @tap="closeMoreActions">
          <text class="action-item-text">取消</text>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 获取页面参数
const pages = getCurrentPages()
const currentPage = pages[pages.length - 1]
const userId = currentPage.options?.id || ''

// 响应式数据
const userInfo = ref({
  id: '',
  name: '',
  age: 0,
  photos: [],
  location: '',
  distance: '',
  lastActive: '',
  isOnline: false,
  isVip: false,
  verified: false,
  introduction: '',
  tags: [],
  height: '',
  education: '',
  occupation: '',
  income: '',
  constellation: '',
  hobbies: [],
  requirements: ''
})

const isLiked = ref(false)
const morePopup = ref(null)

// 方法
const goBack = () => {
  uni.navigateBack()
}

const showMoreActions = () => {
  morePopup.value?.open()
}

const closeMoreActions = () => {
  morePopup.value?.close()
}

const sendGift = () => {
  uni.showToast({ title: '送礼物功能开发中', icon: 'none' })
}

const startChat = () => {
  uni.navigateTo({ url: `/pages/dating/chat?id=${userInfo.value.id}` })
}

const toggleLike = () => {
  isLiked.value = !isLiked.value
  uni.showToast({ 
    title: isLiked.value ? '已喜欢' : '取消喜欢', 
    icon: 'success' 
  })
}

const reportUser = () => {
  closeMoreActions()
  uni.showToast({ title: '举报成功', icon: 'success' })
}

const blockUser = () => {
  closeMoreActions()
  uni.showModal({
    title: '确认拉黑',
    content: '拉黑后将不再收到该用户的消息',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({ title: '已拉黑', icon: 'success' })
      }
    }
  })
}

// 获取用户详情
const getUserDetail = async () => {
  // 模拟API调用
  const mockUser = {
    id: userId,
    name: '小雨',
    age: 25,
    photos: [
      'https://picsum.photos/400/600?random=1',
      'https://picsum.photos/400/600?random=2',
      'https://picsum.photos/400/600?random=3'
    ],
    location: '北京市朝阳区',
    distance: '2.5km',
    lastActive: '2小时前在线',
    isOnline: true,
    isVip: true,
    verified: true,
    introduction: '喜欢旅行和摄影，希望找到一个能一起看世界的人。工作之余喜欢健身和读书，相信生活需要仪式感。',
    tags: ['温柔', '善良', '有趣', '阳光'],
    height: '165cm',
    education: '本科',
    occupation: '设计师',
    income: '10-15K',
    constellation: '天秤座',
    hobbies: ['旅行', '摄影', '健身', '读书', '美食', '电影'],
    requirements: '希望找到一个有责任心、幽默风趣的人，年龄在25-35岁之间，有稳定的工作和积极的生活态度。'
  }
  
  userInfo.value = mockUser
}

onMounted(() => {
  getUserDetail()
})
</script>

<style lang="scss" scoped>
.profile-page {
  min-height: 100vh;
  background: #f8f9fa;
}

.nav-actions {
  display: flex;
  align-items: center;
}

.nav-icon {
  font-size: 32rpx;
  color: #fff;
  padding: 16rpx;
}

// 头部区域
.profile-header {
  position: relative;
  background: #fff;
}

.avatar-container {
  position: relative;
  height: 600rpx;
}

.avatar-swiper {
  width: 100%;
  height: 100%;
}

.profile-avatar {
  width: 100%;
  height: 100%;
}

.online-indicator {
  position: absolute;
  top: 100rpx;
  right: 30rpx;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 30rpx;
  padding: 12rpx 20rpx;
  display: flex;
  align-items: center;
}

.online-dot {
  width: 16rpx;
  height: 16rpx;
  background: #2ed573;
  border-radius: 50%;
  margin-right: 8rpx;
}

.online-text {
  color: #fff;
  font-size: 24rpx;
}

.vip-badge {
  position: absolute;
  top: 160rpx;
  right: 30rpx;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  border-radius: 30rpx;
  padding: 12rpx 20rpx;
  display: flex;
  align-items: center;
}

.vip-text {
  color: #fff;
  font-size: 24rpx;
  margin-left: 8rpx;
  font-weight: 600;
}

.basic-info {
  padding: 30rpx;
}

.name-section {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.user-name {
  font-size: 40rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-right: 16rpx;
}

.user-age {
  font-size: 32rpx;
  color: #7f8c8d;
  margin-right: 16rpx;
}

.verified-badge {
  background: #3498db;
  color: #fff;
  border-radius: 50%;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
}

.location-info {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.location-icon {
  font-size: 28rpx;
  color: #95a5a6;
  margin-right: 12rpx;
}

.location-text {
  font-size: 28rpx;
  color: #6c757d;
}

.active-text {
  font-size: 26rpx;
  color: #95a5a6;
}

// 操作按钮
.action-section {
  padding: 0 30rpx 30rpx;
  background: #fff;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
  
  &:active {
    transform: scale(0.98);
  }
}

.secondary-btn {
  background: #ecf0f1;
  color: #2c3e50;
}

.primary-btn {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: #fff;
}

.like-btn {
  flex: 0 0 88rpx;
  background: #ff6b6b;
  color: #fff;
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
}

.btn-text {
  font-size: 28rpx;
  font-weight: 500;
}

// 详细信息
.detail-content {
  margin-top: 20rpx;
}

.info-section {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-icon {
  font-size: 32rpx;
  color: #667eea;
  margin-right: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
}

.intro-content {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.intro-text {
  font-size: 28rpx;
  color: #6c757d;
  line-height: 1.6;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag-item {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: #fff;
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
}

.info-list {
  background: #f8f9fa;
  border-radius: 16rpx;
  overflow: hidden;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #ecf0f1;
  
  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  font-size: 28rpx;
  color: #6c757d;
}

.info-value {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
}

.hobbies-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.hobby-item {
  background: #f8f9fa;
  border: 2rpx solid #ecf0f1;
  border-radius: 24rpx;
  padding: 12rpx 24rpx;
}

.hobby-text {
  font-size: 26rpx;
  color: #6c757d;
}

.requirements-content {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.requirements-text {
  font-size: 28rpx;
  color: #6c757d;
  line-height: 1.6;
}

// 更多操作弹窗
.more-actions {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #ecf0f1;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.cancel-item {
    justify-content: center;
    margin-top: 20rpx;
    background: #f8f9fa;
    border-radius: 16rpx;
    padding: 24rpx;
  }
}

.action-item-icon {
  font-size: 32rpx;
  color: #e74c3c;
  margin-right: 16rpx;
}

.action-item-text {
  font-size: 30rpx;
  color: #2c3e50;
}

.cancel-item .action-item-text {
  color: #6c757d;
}
</style>