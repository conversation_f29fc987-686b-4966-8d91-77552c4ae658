<template>
  <view class="dating-avatar" :class="avatarClasses" @tap="handleClick">
    <image 
      :src="src" 
      :mode="mode"
      class="avatar-image"
      @error="handleError"
      @load="handleLoad"
    />
    
    <!-- 在线状态指示器 -->
    <view 
      v-if="showOnlineStatus && isOnline !== null" 
      class="online-indicator"
      :class="onlineStatusClass"
    >
      <view class="online-dot"></view>
    </view>
    
    <!-- VIP 徽章 -->
    <view v-if="showVipBadge && isVip" class="vip-badge">
      <text class="i-carbon-star-filled vip-icon"></text>
    </view>
    
    <!-- 认证徽章 -->
    <view v-if="showVerifiedBadge && isVerified" class="verified-badge">
      <text class="i-carbon-checkmark-filled verified-icon"></text>
    </view>
    
    <!-- 加载状态 -->
    <view v-if="loading" class="avatar-loading">
      <view class="loading-spinner"></view>
    </view>
    
    <!-- 错误状态 -->
    <view v-if="error" class="avatar-error">
      <text class="i-carbon-user error-icon"></text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'

interface Props {
  src: string
  size?: 'xs' | 'sm' | 'base' | 'md' | 'lg' | 'xl' | '2xl'
  shape?: 'circle' | 'rounded' | 'square'
  mode?: string
  isOnline?: boolean | null
  isVip?: boolean
  isVerified?: boolean
  showOnlineStatus?: boolean
  showVipBadge?: boolean
  showVerifiedBadge?: boolean
  clickable?: boolean
  bordered?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'base',
  shape: 'circle',
  mode: 'aspectFill',
  isOnline: null,
  isVip: false,
  isVerified: false,
  showOnlineStatus: true,
  showVipBadge: true,
  showVerifiedBadge: true,
  clickable: false,
  bordered: false
})

interface Emits {
  (e: 'click'): void
  (e: 'load'): void
  (e: 'error'): void
}

const emit = defineEmits<Emits>()

const loading = ref(false)
const error = ref(false)

// 计算头像样式类
const avatarClasses = computed(() => {
  return [
    `avatar-${props.size}`,
    `avatar-${props.shape}`,
    {
      'avatar-clickable': props.clickable,
      'avatar-bordered': props.bordered,
      'avatar-loading': loading.value,
      'avatar-error': error.value
    }
  ]
})

// 计算在线状态样式类
const onlineStatusClass = computed(() => {
  if (props.isOnline === null) return ''
  return props.isOnline ? 'status-online' : 'status-offline'
})

// 处理点击事件
const handleClick = () => {
  if (props.clickable) {
    emit('click')
  }
}

// 处理图片加载成功
const handleLoad = () => {
  loading.value = false
  error.value = false
  emit('load')
}

// 处理图片加载失败
const handleError = () => {
  loading.value = false
  error.value = true
  emit('error')
}

// 开始加载
loading.value = true
</script>

<style lang="scss" scoped>
@import './design-system.scss';

.dating-avatar {
  position: relative;
  display: inline-block;
  overflow: hidden;
  transition: all $dating-transition-base;
  
  // 尺寸变体
  &.avatar-xs {
    width: $dating-avatar-xs;
    height: $dating-avatar-xs;
  }
  
  &.avatar-sm {
    width: $dating-avatar-sm;
    height: $dating-avatar-sm;
  }
  
  &.avatar-base {
    width: $dating-avatar-base;
    height: $dating-avatar-base;
  }
  
  &.avatar-md {
    width: $dating-avatar-md;
    height: $dating-avatar-md;
  }
  
  &.avatar-lg {
    width: $dating-avatar-lg;
    height: $dating-avatar-lg;
  }
  
  &.avatar-xl {
    width: $dating-avatar-xl;
    height: $dating-avatar-xl;
  }
  
  &.avatar-2xl {
    width: $dating-avatar-2xl;
    height: $dating-avatar-2xl;
  }
  
  // 形状变体
  &.avatar-circle {
    border-radius: $dating-radius-full;
  }
  
  &.avatar-rounded {
    border-radius: $dating-radius-md;
  }
  
  &.avatar-square {
    border-radius: $dating-radius-sm;
  }
  
  // 可点击状态
  &.avatar-clickable {
    cursor: pointer;
    
    &:active {
      transform: scale(0.95);
    }
  }
  
  // 边框样式
  &.avatar-bordered {
    border: 4rpx solid $dating-white;
    box-shadow: $dating-shadow-base;
  }
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

// 在线状态指示器
.online-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 24rpx;
  height: 24rpx;
  border-radius: $dating-radius-full;
  border: 3rpx solid $dating-white;
  @include dating-flex-center;
  
  &.status-online {
    background: $dating-online;
  }
  
  &.status-offline {
    background: $dating-offline;
  }
}

.online-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: $dating-radius-full;
  background: $dating-white;
}

// VIP 徽章
.vip-badge {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 32rpx;
  height: 32rpx;
  background: $dating-gradient-warm;
  border-radius: $dating-radius-full;
  border: 2rpx solid $dating-white;
  @include dating-flex-center;
  box-shadow: $dating-shadow-sm;
}

.vip-icon {
  font-size: 16rpx;
  color: $dating-white;
}

// 认证徽章
.verified-badge {
  position: absolute;
  bottom: -4rpx;
  right: -4rpx;
  width: 28rpx;
  height: 28rpx;
  background: $dating-success;
  border-radius: $dating-radius-full;
  border: 2rpx solid $dating-white;
  @include dating-flex-center;
  box-shadow: $dating-shadow-sm;
}

.verified-icon {
  font-size: 14rpx;
  color: $dating-white;
}

// 加载状态
.avatar-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: $dating-gray-100;
  @include dating-flex-center;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid $dating-gray-300;
  border-top: 3rpx solid $dating-primary;
  border-radius: $dating-radius-full;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 错误状态
.avatar-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: $dating-gray-200;
  @include dating-flex-center;
}

.error-icon {
  font-size: 32rpx;
  color: $dating-gray-500;
}
</style>
