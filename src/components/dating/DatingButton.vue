<template>
  <view 
    class="dating-button" 
    :class="buttonClasses"
    @tap="handleClick"
  >
    <!-- 加载状态 -->
    <view v-if="loading" class="button-loading">
      <view class="loading-spinner"></view>
    </view>
    
    <!-- 图标 -->
    <text v-if="icon && !loading" :class="['button-icon', icon]"></text>
    
    <!-- 文本内容 -->
    <text v-if="$slots.default || text" class="button-text">
      <slot>{{ text }}</slot>
    </text>
    
    <!-- 右侧图标 -->
    <text v-if="rightIcon && !loading" :class="['button-right-icon', rightIcon]"></text>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  text?: string
  type?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'ghost' | 'text'
  size?: 'sm' | 'base' | 'lg'
  variant?: 'solid' | 'outline' | 'ghost' | 'gradient'
  shape?: 'rounded' | 'pill' | 'square'
  icon?: string
  rightIcon?: string
  loading?: boolean
  disabled?: boolean
  block?: boolean
  shadow?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  size: 'base',
  variant: 'solid',
  shape: 'rounded',
  loading: false,
  disabled: false,
  block: false,
  shadow: false
})

interface Emits {
  (e: 'click'): void
}

const emit = defineEmits<Emits>()

// 计算按钮样式类
const buttonClasses = computed(() => {
  return [
    `btn-${props.type}`,
    `btn-${props.size}`,
    `btn-${props.variant}`,
    `btn-${props.shape}`,
    {
      'btn-loading': props.loading,
      'btn-disabled': props.disabled,
      'btn-block': props.block,
      'btn-shadow': props.shadow,
      'btn-icon-only': (props.icon || props.rightIcon) && !props.text && !props.$slots.default
    }
  ]
})

// 处理点击事件
const handleClick = () => {
  if (!props.disabled && !props.loading) {
    emit('click')
  }
}
</script>

<style lang="scss" scoped>
@import './design-system.scss';

.dating-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: $dating-space-2;
  border: none;
  font-weight: $dating-font-medium;
  transition: all $dating-transition-base;
  cursor: pointer;
  user-select: none;
  position: relative;
  overflow: hidden;
  
  // 尺寸变体
  &.btn-sm {
    height: $dating-btn-sm;
    padding: 0 $dating-space-4;
    font-size: $dating-font-sm;
    
    &.btn-icon-only {
      width: $dating-btn-sm;
      padding: 0;
    }
  }
  
  &.btn-base {
    height: $dating-btn-base;
    padding: 0 $dating-space-6;
    font-size: $dating-font-base;
    
    &.btn-icon-only {
      width: $dating-btn-base;
      padding: 0;
    }
  }
  
  &.btn-lg {
    height: $dating-btn-lg;
    padding: 0 $dating-space-8;
    font-size: $dating-font-md;
    
    &.btn-icon-only {
      width: $dating-btn-lg;
      padding: 0;
    }
  }
  
  // 形状变体
  &.btn-rounded {
    border-radius: $dating-radius-md;
  }
  
  &.btn-pill {
    border-radius: $dating-radius-full;
  }
  
  &.btn-square {
    border-radius: $dating-radius-sm;
  }
  
  // 块级按钮
  &.btn-block {
    width: 100%;
    display: flex;
  }
  
  // 阴影效果
  &.btn-shadow {
    box-shadow: $dating-shadow-base;
    
    &:active {
      box-shadow: $dating-shadow-sm;
    }
  }
  
  // 类型和变体组合
  // Primary 主要按钮
  &.btn-primary.btn-solid {
    background: $dating-primary;
    color: $dating-white;
    
    &:active {
      background: $dating-primary-dark;
      transform: scale(0.95);
    }
  }
  
  &.btn-primary.btn-gradient {
    background: $dating-gradient-primary;
    color: $dating-white;
    
    &:active {
      transform: scale(0.95);
    }
  }
  
  &.btn-primary.btn-outline {
    background: transparent;
    color: $dating-primary;
    border: 2rpx solid $dating-primary;
    
    &:active {
      background: $dating-primary;
      color: $dating-white;
    }
  }
  
  &.btn-primary.btn-ghost {
    background: rgba($dating-primary, 0.1);
    color: $dating-primary;
    
    &:active {
      background: rgba($dating-primary, 0.2);
    }
  }
  
  // Secondary 次要按钮
  &.btn-secondary.btn-solid {
    background: $dating-secondary;
    color: $dating-white;
    
    &:active {
      background: $dating-secondary-dark;
      transform: scale(0.95);
    }
  }
  
  &.btn-secondary.btn-gradient {
    background: $dating-gradient-secondary;
    color: $dating-white;
    
    &:active {
      transform: scale(0.95);
    }
  }
  
  &.btn-secondary.btn-outline {
    background: transparent;
    color: $dating-secondary;
    border: 2rpx solid $dating-secondary;
    
    &:active {
      background: $dating-secondary;
      color: $dating-white;
    }
  }
  
  // Success 成功按钮
  &.btn-success.btn-solid {
    background: $dating-success;
    color: $dating-white;
    
    &:active {
      background: darken($dating-success, 10%);
      transform: scale(0.95);
    }
  }
  
  // Warning 警告按钮
  &.btn-warning.btn-solid {
    background: $dating-warning;
    color: $dating-white;
    
    &:active {
      background: darken($dating-warning, 10%);
      transform: scale(0.95);
    }
  }
  
  // Error 错误按钮
  &.btn-error.btn-solid {
    background: $dating-error;
    color: $dating-white;
    
    &:active {
      background: darken($dating-error, 10%);
      transform: scale(0.95);
    }
  }
  
  // Ghost 幽灵按钮
  &.btn-ghost {
    background: transparent;
    color: $dating-gray-600;
    
    &:active {
      background: $dating-gray-100;
    }
  }
  
  // Text 文本按钮
  &.btn-text {
    background: transparent;
    color: $dating-primary;
    padding: $dating-space-2 $dating-space-3;
    
    &:active {
      background: rgba($dating-primary, 0.1);
    }
  }
  
  // 禁用状态
  &.btn-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    
    &:active {
      transform: none;
    }
  }
  
  // 加载状态
  &.btn-loading {
    cursor: not-allowed;
    
    .button-text,
    .button-icon,
    .button-right-icon {
      opacity: 0;
    }
  }
}

// 按钮内容
.button-text {
  line-height: 1;
}

.button-icon,
.button-right-icon {
  font-size: 1.2em;
}

// 加载动画
.button-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.loading-spinner {
  width: 24rpx;
  height: 24rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid currentColor;
  border-radius: $dating-radius-full;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
