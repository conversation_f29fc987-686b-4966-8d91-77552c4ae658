// 交友模块设计系统
// Dating Module Design System

// ==================== 色彩系统 ====================
// Primary Colors - 主色调
$dating-primary: #FF6B6B;           // 主要品牌色 - 温暖的珊瑚红
$dating-primary-light: #FF8E8E;     // 主色调浅色
$dating-primary-dark: #E55555;      // 主色调深色
$dating-primary-gradient: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);

// Secondary Colors - 辅助色
$dating-secondary: #4ECDC4;         // 辅助色 - 清新薄荷绿
$dating-secondary-light: #7EDDD6;   // 辅助色浅色
$dating-secondary-dark: #3CBAB1;    // 辅助色深色

// Accent Colors - 强调色
$dating-accent: #FFE66D;            // 强调色 - 温暖黄色
$dating-accent-light: #FFED8A;      // 强调色浅色
$dating-accent-dark: #E6CF5A;       // 强调色深色

// Neutral Colors - 中性色
$dating-white: #FFFFFF;
$dating-gray-50: #FAFAFA;
$dating-gray-100: #F5F5F5;
$dating-gray-200: #EEEEEE;
$dating-gray-300: #E0E0E0;
$dating-gray-400: #BDBDBD;
$dating-gray-500: #9E9E9E;
$dating-gray-600: #757575;
$dating-gray-700: #616161;
$dating-gray-800: #424242;
$dating-gray-900: #212121;

// Status Colors - 状态色
$dating-success: #4CAF50;
$dating-warning: #FF9800;
$dating-error: #F44336;
$dating-info: #2196F3;

// Online Status - 在线状态
$dating-online: #4CAF50;
$dating-offline: #9E9E9E;
$dating-away: #FF9800;

// ==================== 字体系统 ====================
// Font Sizes - 字体大小 (rpx)
$dating-font-xs: 20rpx;      // 极小文字
$dating-font-sm: 24rpx;      // 小文字
$dating-font-base: 28rpx;    // 基础文字
$dating-font-md: 32rpx;      // 中等文字
$dating-font-lg: 36rpx;      // 大文字
$dating-font-xl: 40rpx;      // 超大文字
$dating-font-2xl: 48rpx;     // 标题文字
$dating-font-3xl: 56rpx;     // 大标题文字

// Font Weights - 字体权重
$dating-font-light: 300;
$dating-font-normal: 400;
$dating-font-medium: 500;
$dating-font-semibold: 600;
$dating-font-bold: 700;

// Line Heights - 行高
$dating-leading-tight: 1.2;
$dating-leading-normal: 1.4;
$dating-leading-relaxed: 1.6;
$dating-leading-loose: 1.8;

// ==================== 间距系统 ====================
// Spacing Scale - 间距比例 (rpx)
$dating-space-1: 4rpx;
$dating-space-2: 8rpx;
$dating-space-3: 12rpx;
$dating-space-4: 16rpx;
$dating-space-5: 20rpx;
$dating-space-6: 24rpx;
$dating-space-8: 32rpx;
$dating-space-10: 40rpx;
$dating-space-12: 48rpx;
$dating-space-16: 64rpx;
$dating-space-20: 80rpx;
$dating-space-24: 96rpx;
$dating-space-32: 128rpx;

// ==================== 圆角系统 ====================
// Border Radius - 圆角
$dating-radius-none: 0;
$dating-radius-sm: 8rpx;
$dating-radius-base: 12rpx;
$dating-radius-md: 16rpx;
$dating-radius-lg: 20rpx;
$dating-radius-xl: 24rpx;
$dating-radius-2xl: 32rpx;
$dating-radius-full: 9999rpx;

// ==================== 阴影系统 ====================
// Box Shadows - 阴影
$dating-shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
$dating-shadow-base: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
$dating-shadow-md: 0 8rpx 24rpx rgba(0, 0, 0, 0.10);
$dating-shadow-lg: 0 12rpx 32rpx rgba(0, 0, 0, 0.12);
$dating-shadow-xl: 0 16rpx 48rpx rgba(0, 0, 0, 0.15);

// Card Shadows - 卡片阴影
$dating-card-shadow: 0 4rpx 20rpx rgba(255, 107, 107, 0.15);
$dating-card-shadow-hover: 0 8rpx 32rpx rgba(255, 107, 107, 0.20);

// ==================== 动画系统 ====================
// Transitions - 过渡动画
$dating-transition-fast: 0.15s ease-out;
$dating-transition-base: 0.25s ease-out;
$dating-transition-slow: 0.35s ease-out;

// Easing Functions - 缓动函数
$dating-ease-in: cubic-bezier(0.4, 0, 1, 1);
$dating-ease-out: cubic-bezier(0, 0, 0.2, 1);
$dating-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
$dating-ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

// ==================== 组件尺寸 ====================
// Avatar Sizes - 头像尺寸
$dating-avatar-xs: 48rpx;
$dating-avatar-sm: 64rpx;
$dating-avatar-base: 80rpx;
$dating-avatar-md: 96rpx;
$dating-avatar-lg: 120rpx;
$dating-avatar-xl: 160rpx;
$dating-avatar-2xl: 200rpx;

// Button Heights - 按钮高度
$dating-btn-sm: 64rpx;
$dating-btn-base: 80rpx;
$dating-btn-lg: 96rpx;

// Card Sizes - 卡片尺寸
$dating-card-padding: $dating-space-6;
$dating-card-radius: $dating-radius-lg;

// ==================== 渐变系统 ====================
// Gradient Backgrounds - 渐变背景
$dating-gradient-primary: linear-gradient(135deg, $dating-primary 0%, $dating-primary-light 100%);
$dating-gradient-secondary: linear-gradient(135deg, $dating-secondary 0%, $dating-secondary-light 100%);
$dating-gradient-warm: linear-gradient(135deg, #FF6B6B 0%, #FFE66D 100%);
$dating-gradient-cool: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%);
$dating-gradient-sunset: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 50%, #FFE66D 100%);

// Page Backgrounds - 页面背景
$dating-bg-primary: linear-gradient(180deg, #FF6B6B 0%, #FF8E8E 100%);
$dating-bg-secondary: linear-gradient(180deg, #F8F9FA 0%, #FFFFFF 100%);
$dating-bg-card: $dating-white;

// ==================== Z-Index 层级 ====================
$dating-z-dropdown: 1000;
$dating-z-sticky: 1020;
$dating-z-fixed: 1030;
$dating-z-modal-backdrop: 1040;
$dating-z-modal: 1050;
$dating-z-popover: 1060;
$dating-z-tooltip: 1070;

// ==================== 响应式断点 ====================
// Breakpoints - 断点
$dating-screen-sm: 576px;
$dating-screen-md: 768px;
$dating-screen-lg: 992px;
$dating-screen-xl: 1200px;

// ==================== 通用混合器 ====================
// Card Mixin - 卡片混合器
@mixin dating-card {
  background: $dating-bg-card;
  border-radius: $dating-card-radius;
  box-shadow: $dating-card-shadow;
  padding: $dating-card-padding;
  transition: all $dating-transition-base;
  
  &:hover {
    box-shadow: $dating-card-shadow-hover;
    transform: translateY(-2rpx);
  }
}

// Button Mixin - 按钮混合器
@mixin dating-button($bg-color: $dating-primary, $text-color: $dating-white) {
  background: $bg-color;
  color: $text-color;
  border: none;
  border-radius: $dating-radius-full;
  font-weight: $dating-font-medium;
  transition: all $dating-transition-base;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:active {
    transform: scale(0.95);
  }
}

// Flex Center Mixin - 居中混合器
@mixin dating-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// Text Truncate Mixin - 文本截断混合器
@mixin dating-text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Glass Effect Mixin - 毛玻璃效果混合器
@mixin dating-glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}
