<template>
  <view 
    class="dating-tag" 
    :class="tagClasses"
    @tap="handleClick"
  >
    <!-- 图标 -->
    <text v-if="icon" :class="['tag-icon', icon]"></text>
    
    <!-- 文本内容 -->
    <text class="tag-text">
      <slot>{{ text }}</slot>
    </text>
    
    <!-- 关闭按钮 -->
    <text 
      v-if="closable" 
      class="i-carbon-close tag-close"
      @tap.stop="handleClose"
    ></text>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  text?: string
  type?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error'
  variant?: 'solid' | 'outline' | 'ghost'
  size?: 'sm' | 'base' | 'lg'
  shape?: 'rounded' | 'pill'
  icon?: string
  closable?: boolean
  clickable?: boolean
  selected?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default',
  variant: 'solid',
  size: 'base',
  shape: 'rounded',
  closable: false,
  clickable: false,
  selected: false
})

interface Emits {
  (e: 'click'): void
  (e: 'close'): void
}

const emit = defineEmits<Emits>()

// 计算标签样式类
const tagClasses = computed(() => {
  return [
    `tag-${props.type}`,
    `tag-${props.variant}`,
    `tag-${props.size}`,
    `tag-${props.shape}`,
    {
      'tag-clickable': props.clickable,
      'tag-selected': props.selected,
      'tag-closable': props.closable
    }
  ]
})

// 处理点击事件
const handleClick = () => {
  if (props.clickable) {
    emit('click')
  }
}

// 处理关闭事件
const handleClose = () => {
  emit('close')
}
</script>

<style lang="scss" scoped>
@import './design-system.scss';

.dating-tag {
  display: inline-flex;
  align-items: center;
  gap: $dating-space-1;
  font-weight: $dating-font-medium;
  transition: all $dating-transition-base;
  user-select: none;
  
  // 尺寸变体
  &.tag-sm {
    padding: $dating-space-1 $dating-space-2;
    font-size: $dating-font-xs;
    height: 32rpx;
  }
  
  &.tag-base {
    padding: $dating-space-2 $dating-space-3;
    font-size: $dating-font-sm;
    height: 40rpx;
  }
  
  &.tag-lg {
    padding: $dating-space-3 $dating-space-4;
    font-size: $dating-font-base;
    height: 48rpx;
  }
  
  // 形状变体
  &.tag-rounded {
    border-radius: $dating-radius-sm;
  }
  
  &.tag-pill {
    border-radius: $dating-radius-full;
  }
  
  // 可点击状态
  &.tag-clickable {
    cursor: pointer;
    
    &:active {
      transform: scale(0.95);
    }
  }
  
  // 选中状态
  &.tag-selected {
    transform: scale(1.05);
    box-shadow: $dating-shadow-base;
  }
  
  // 可关闭状态
  &.tag-closable {
    padding-right: $dating-space-2;
  }
  
  // 类型和变体组合
  // Default 默认标签
  &.tag-default.tag-solid {
    background: $dating-gray-200;
    color: $dating-gray-700;
    
    &:hover {
      background: $dating-gray-300;
    }
    
    &.tag-selected {
      background: $dating-primary;
      color: $dating-white;
    }
  }
  
  &.tag-default.tag-outline {
    background: transparent;
    color: $dating-gray-600;
    border: 1rpx solid $dating-gray-300;
    
    &:hover {
      border-color: $dating-gray-400;
    }
    
    &.tag-selected {
      background: $dating-primary;
      color: $dating-white;
      border-color: $dating-primary;
    }
  }
  
  &.tag-default.tag-ghost {
    background: rgba($dating-gray-500, 0.1);
    color: $dating-gray-600;
    
    &:hover {
      background: rgba($dating-gray-500, 0.15);
    }
    
    &.tag-selected {
      background: rgba($dating-primary, 0.15);
      color: $dating-primary;
    }
  }
  
  // Primary 主要标签
  &.tag-primary.tag-solid {
    background: $dating-primary;
    color: $dating-white;
    
    &:hover {
      background: $dating-primary-dark;
    }
  }
  
  &.tag-primary.tag-outline {
    background: transparent;
    color: $dating-primary;
    border: 1rpx solid $dating-primary;
    
    &:hover {
      background: $dating-primary;
      color: $dating-white;
    }
  }
  
  &.tag-primary.tag-ghost {
    background: rgba($dating-primary, 0.1);
    color: $dating-primary;
    
    &:hover {
      background: rgba($dating-primary, 0.15);
    }
  }
  
  // Secondary 次要标签
  &.tag-secondary.tag-solid {
    background: $dating-secondary;
    color: $dating-white;
    
    &:hover {
      background: $dating-secondary-dark;
    }
  }
  
  &.tag-secondary.tag-outline {
    background: transparent;
    color: $dating-secondary;
    border: 1rpx solid $dating-secondary;
    
    &:hover {
      background: $dating-secondary;
      color: $dating-white;
    }
  }
  
  &.tag-secondary.tag-ghost {
    background: rgba($dating-secondary, 0.1);
    color: $dating-secondary;
    
    &:hover {
      background: rgba($dating-secondary, 0.15);
    }
  }
  
  // Success 成功标签
  &.tag-success.tag-solid {
    background: $dating-success;
    color: $dating-white;
  }
  
  &.tag-success.tag-outline {
    background: transparent;
    color: $dating-success;
    border: 1rpx solid $dating-success;
  }
  
  &.tag-success.tag-ghost {
    background: rgba($dating-success, 0.1);
    color: $dating-success;
  }
  
  // Warning 警告标签
  &.tag-warning.tag-solid {
    background: $dating-warning;
    color: $dating-white;
  }
  
  &.tag-warning.tag-outline {
    background: transparent;
    color: $dating-warning;
    border: 1rpx solid $dating-warning;
  }
  
  &.tag-warning.tag-ghost {
    background: rgba($dating-warning, 0.1);
    color: $dating-warning;
  }
  
  // Error 错误标签
  &.tag-error.tag-solid {
    background: $dating-error;
    color: $dating-white;
  }
  
  &.tag-error.tag-outline {
    background: transparent;
    color: $dating-error;
    border: 1rpx solid $dating-error;
  }
  
  &.tag-error.tag-ghost {
    background: rgba($dating-error, 0.1);
    color: $dating-error;
  }
}

// 标签内容
.tag-text {
  line-height: 1;
  white-space: nowrap;
}

.tag-icon {
  font-size: 0.9em;
}

.tag-close {
  font-size: 0.8em;
  margin-left: $dating-space-1;
  opacity: 0.7;
  transition: opacity $dating-transition-fast;
  
  &:hover {
    opacity: 1;
  }
}
</style>
