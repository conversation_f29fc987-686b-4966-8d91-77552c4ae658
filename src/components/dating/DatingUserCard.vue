<template>
  <view class="dating-user-card" :class="cardClasses" @tap="handleCardClick">
    <!-- 卡片头部 - 用户照片 -->
    <view class="card-header">
      <image 
        :src="user.avatar || user.photos?.[0]" 
        mode="aspectFill" 
        class="user-photo"
        @error="handlePhotoError"
      />
      
      <!-- 照片指示器 -->
      <view v-if="user.photos && user.photos.length > 1" class="photo-indicators">
        <view 
          v-for="(photo, index) in user.photos" 
          :key="index"
          class="photo-dot"
          :class="{ active: currentPhotoIndex === index }"
        ></view>
      </view>
      
      <!-- 状态徽章 -->
      <view class="status-badges">
        <view v-if="user.isOnline" class="online-badge">
          <view class="online-dot"></view>
          <text class="online-text">在线</text>
        </view>
        <view v-if="user.isVip" class="vip-badge">
          <text class="i-carbon-star-filled vip-icon"></text>
          <text class="vip-text">VIP</text>
        </view>
        <view v-if="user.verified" class="verified-badge">
          <text class="i-carbon-checkmark-filled verified-icon"></text>
        </view>
      </view>
      
      <!-- 距离标签 -->
      <view v-if="user.distance" class="distance-tag">
        <text class="i-carbon-location distance-icon"></text>
        <text class="distance-text">{{ user.distance }}</text>
      </view>
    </view>
    
    <!-- 卡片内容 -->
    <view class="card-content">
      <!-- 基本信息 -->
      <view class="user-basic">
        <text class="user-name">{{ user.name }}</text>
        <text class="user-age">{{ user.age }}岁</text>
      </view>
      
      <!-- 位置信息 -->
      <view v-if="user.location" class="user-location">
        <text class="i-carbon-location location-icon"></text>
        <text class="location-text">{{ user.location }}</text>
      </view>
      
      <!-- 标签 -->
      <view v-if="user.tags && user.tags.length" class="user-tags">
        <text 
          v-for="tag in user.tags.slice(0, maxTags)" 
          :key="tag"
          class="user-tag"
        >
          {{ tag }}
        </text>
        <text v-if="user.tags.length > maxTags" class="more-tags">
          +{{ user.tags.length - maxTags }}
        </text>
      </view>
      
      <!-- 简介 -->
      <text v-if="user.introduction && showIntroduction" class="user-intro">
        {{ user.introduction }}
      </text>
    </view>
    
    <!-- 卡片操作 -->
    <view v-if="showActions" class="card-actions">
      <view class="action-btn dislike-btn" @tap.stop="handleDislike">
        <text class="i-carbon-close action-icon"></text>
      </view>
      <view class="action-btn chat-btn" @tap.stop="handleChat">
        <text class="i-carbon-chat action-icon"></text>
      </view>
      <view class="action-btn like-btn" @tap.stop="handleLike">
        <text class="i-carbon-favorite action-icon"></text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'

interface User {
  id: string
  name: string
  age: number
  avatar?: string
  photos?: string[]
  location?: string
  distance?: string
  tags?: string[]
  introduction?: string
  isOnline?: boolean
  isVip?: boolean
  verified?: boolean
}

interface Props {
  user: User
  variant?: 'default' | 'compact' | 'detailed'
  showActions?: boolean
  showIntroduction?: boolean
  maxTags?: number
  clickable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  showActions: true,
  showIntroduction: true,
  maxTags: 3,
  clickable: true
})

interface Emits {
  (e: 'click', user: User): void
  (e: 'like', userId: string): void
  (e: 'dislike', userId: string): void
  (e: 'chat', userId: string): void
}

const emit = defineEmits<Emits>()

const currentPhotoIndex = ref(0)

// 计算卡片样式类
const cardClasses = computed(() => {
  return [
    `card-${props.variant}`,
    {
      'card-clickable': props.clickable
    }
  ]
})

// 处理卡片点击
const handleCardClick = () => {
  if (props.clickable) {
    emit('click', props.user)
  }
}

// 处理喜欢
const handleLike = () => {
  emit('like', props.user.id)
}

// 处理不喜欢
const handleDislike = () => {
  emit('dislike', props.user.id)
}

// 处理聊天
const handleChat = () => {
  emit('chat', props.user.id)
}

// 处理照片加载错误
const handlePhotoError = () => {
  console.log('Photo load error for user:', props.user.id)
}
</script>

<style lang="scss" scoped>
@import './design-system.scss';

.dating-user-card {
  @include dating-card;
  overflow: hidden;
  margin-bottom: $dating-space-5;
  
  &.card-clickable {
    cursor: pointer;
    
    &:active {
      transform: translateY(-1rpx) scale(0.98);
    }
  }
  
  // 变体样式
  &.card-compact {
    .card-header {
      height: 300rpx;
    }
    
    .card-content {
      padding: $dating-space-4;
    }
  }
  
  &.card-detailed {
    .card-header {
      height: 500rpx;
    }
    
    .card-content {
      padding: $dating-space-8;
    }
  }
}

// 卡片头部
.card-header {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.user-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

// 照片指示器
.photo-indicators {
  position: absolute;
  top: $dating-space-4;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: $dating-space-2;
}

.photo-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: $dating-radius-full;
  background: rgba(255, 255, 255, 0.5);
  transition: all $dating-transition-fast;
  
  &.active {
    background: $dating-white;
    width: 24rpx;
  }
}

// 状态徽章
.status-badges {
  position: absolute;
  top: $dating-space-4;
  right: $dating-space-4;
  display: flex;
  flex-direction: column;
  gap: $dating-space-2;
}

.online-badge {
  @include dating-glass-effect;
  border-radius: $dating-radius-full;
  padding: $dating-space-2 $dating-space-3;
  display: flex;
  align-items: center;
  gap: $dating-space-2;
}

.online-dot {
  width: 12rpx;
  height: 12rpx;
  background: $dating-online;
  border-radius: $dating-radius-full;
}

.online-text {
  color: $dating-white;
  font-size: $dating-font-xs;
  font-weight: $dating-font-medium;
}

.vip-badge {
  background: $dating-gradient-warm;
  border-radius: $dating-radius-full;
  padding: $dating-space-2 $dating-space-3;
  display: flex;
  align-items: center;
  gap: $dating-space-1;
}

.vip-icon {
  font-size: $dating-font-xs;
  color: $dating-white;
}

.vip-text {
  color: $dating-white;
  font-size: $dating-font-xs;
  font-weight: $dating-font-bold;
}

.verified-badge {
  width: 32rpx;
  height: 32rpx;
  background: $dating-success;
  border-radius: $dating-radius-full;
  @include dating-flex-center;
}

.verified-icon {
  font-size: $dating-font-sm;
  color: $dating-white;
}

// 距离标签
.distance-tag {
  position: absolute;
  bottom: $dating-space-4;
  right: $dating-space-4;
  @include dating-glass-effect;
  border-radius: $dating-radius-full;
  padding: $dating-space-2 $dating-space-3;
  display: flex;
  align-items: center;
  gap: $dating-space-1;
}

.distance-icon {
  font-size: $dating-font-sm;
  color: $dating-white;
}

.distance-text {
  color: $dating-white;
  font-size: $dating-font-sm;
  font-weight: $dating-font-medium;
}

// 卡片内容
.card-content {
  padding: $dating-space-6;
}

.user-basic {
  display: flex;
  align-items: center;
  gap: $dating-space-3;
  margin-bottom: $dating-space-4;
}

.user-name {
  font-size: $dating-font-lg;
  font-weight: $dating-font-bold;
  color: $dating-gray-900;
}

.user-age {
  font-size: $dating-font-base;
  color: $dating-gray-600;
}

.user-location {
  display: flex;
  align-items: center;
  gap: $dating-space-2;
  margin-bottom: $dating-space-4;
}

.location-icon {
  font-size: $dating-font-sm;
  color: $dating-gray-500;
}

.location-text {
  font-size: $dating-font-sm;
  color: $dating-gray-600;
}

// 标签
.user-tags {
  display: flex;
  gap: $dating-space-2;
  flex-wrap: wrap;
  margin-bottom: $dating-space-4;
}

.user-tag {
  background: $dating-gray-100;
  color: $dating-gray-700;
  padding: $dating-space-2 $dating-space-3;
  border-radius: $dating-radius-full;
  font-size: $dating-font-xs;
  font-weight: $dating-font-medium;
}

.more-tags {
  background: $dating-primary;
  color: $dating-white;
  padding: $dating-space-2 $dating-space-3;
  border-radius: $dating-radius-full;
  font-size: $dating-font-xs;
  font-weight: $dating-font-medium;
}

// 简介
.user-intro {
  font-size: $dating-font-sm;
  color: $dating-gray-600;
  line-height: $dating-leading-relaxed;
  @include dating-text-truncate;
}

// 卡片操作
.card-actions {
  display: flex;
  justify-content: center;
  gap: $dating-space-8;
  padding: $dating-space-5 $dating-space-6 $dating-space-6;
}

.action-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: $dating-radius-full;
  @include dating-flex-center;
  color: $dating-white;
  font-size: $dating-font-md;
  transition: all $dating-transition-base;
  box-shadow: $dating-shadow-base;
  
  &:active {
    transform: scale(0.9);
  }
}

.dislike-btn {
  background: $dating-gradient-cool;
}

.chat-btn {
  background: $dating-gradient-secondary;
}

.like-btn {
  background: $dating-gradient-primary;
}

.action-icon {
  font-size: $dating-font-md;
}
</style>
